import typescript from "rollup-plugin-typescript2";
import { terser } from "rollup-plugin-terser";
import cleanup from "rollup-plugin-cleanup";
// import babel from "rollup-plugin-babel";
import commonjs from "@rollup/plugin-commonjs";
import resolve from "@rollup/plugin-node-resolve";
import hashbang from "rollup-plugin-hashbang";

export default [
  {
    input: "src/index.ts", // 源文件入口
    output: [
      {
        file: "dist/index.js", // package.json 中 "module": "dist/index.esm.js"
        format: "esm", // es module 形式的包， 用来import 导入， 可以tree shaking
        sourcemap: true,
      },
      {
        file: "dist/cjs/index.cjs", // package.json 中 "main": "dist/index.cjs.js",
        format: "cjs", // commonjs 形式的包， require 导入
        sourcemap: true,
      },
      // {
      //   file: "dist/umd/index.js",
      //   name: "GLWidget",
      //   format: "umd", // umd 兼容形式的包， 可以直接应用于网页 script
      //   sourcemap: true,
      // },
    ],
    plugins: [
      typescript({
        tsconfig: "tsconfig.json",
        declaration: true,
      }),
      terser(),
      cleanup(),
      // babel({
      //   exclude: "node_modules/**",
      // }),
    ],
    external: ["./src/types/CS_2015-12-15.ts", "./src/json/"],
  },
  {
    input: "src/bin/index.ts",
    output: [
      {
        file: "dist/bin.cjs",
        format: "cjs",
        banner: "#!/usr/bin/env node",
      },
    ],
    plugins: [typescript(), terser(), cleanup()],
  },
];
// "lodash": "^4.17.21",
// "@alicloud/console-components": "^1.6.2",
// "moment": "^2.29.4",
