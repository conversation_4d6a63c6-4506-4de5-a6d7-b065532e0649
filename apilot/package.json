{"name": "@ali/apilot", "version": "1.5.18", "description": "阿里云统一api请求SDK", "bin": {"apilotio": "dist/bin.cjs"}, "main": "dist/index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rm -rf dist & rollup -c", "apilotio": "node dist/bin.cjs", "release": "tnpm run build -- -V && tnpm version patch && tnpm publish"}, "author": "", "license": "ISC", "dependencies": {"@ali/console-base-fetcher": "^1.19.0", "@alicloud/console-base-error-prompt-proxy": "^1.9.10", "@alicloud/xconsole-service": "^2.5.10", "axios": "0.21.4", "commander": "^11.0.0", "loadash": "^1.0.0", "swr": "2.2.5", "use-sync-external-store": "^1.2.0"}, "devDependencies": {"@babel/core": "^7.22.10", "@babel/preset-env": "^7.22.10", "@babel/preset-typescript": "^7.22.5", "@rollup/plugin-commonjs": "^25.0.4", "@rollup/plugin-node-resolve": "^15.2.1", "@types/lodash": "^4.14.197", "dts-bundle": "^0.7.3", "rollup": "^3.28.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-hashbang": "^3.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.35.0", "typescript": "^5.1.6"}, "peerDependencies": {"styled-components": "^4.4.1"}, "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "repository": "https://gitlab.alibaba-inc.com/rentingting.rtt/apiservice.git"}