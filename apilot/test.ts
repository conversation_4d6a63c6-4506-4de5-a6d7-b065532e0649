// src test
import { apilot, useApilot } from "./src";
import CS from "./src/types/CS_2015-12-15";

// apilot.create().create().create<CS>()("DescribeClustersV1", {
//   params: {},
// });

// import { apilot, useApilot } from "./dist";
// import { CS } from "./dist/types";
const s1 = apilot.create().create().create<CS>();
const res = await s1("DescribeClusterV2UserKubeconfig", {});
const { data, mutate } = useApilot<CS>("CS", {
  apilot: s1,
})("DescribeClustersV1", {
  params: { page_number: 10 },
});
