// @ts-nocheck
import { createService } from "@alicloud/xconsole-service";
// import * as allJson from './json';
import _ from "lodash";
import { APIInstance, APIOptions, RequestParams, ResponseType, ActionOrActions } from "./types";
import { Actions } from "./types/createServiceInterfaces";
import fetcher from "@ali/console-base-fetcher";
import {
  atlasInterceptor,
  customInterceptor,
  devAtlasInterceptor,
  fecsAtlasInterceptor,
  mockInterceptor,
  preFecsOpenApiInterceptor,
} from "./interceptors";
import { updateConfig } from "./util";
import axios from "axios";
import { handleError } from "./error";
import { createFetcher } from "@alicloud/console-fetcher-proxy";
const { callOpenApi } = createFetcher({}, {}, { riskVersion: '3.0' });

export class ApiService<PCode> {
  public config: APIOptions;

  constructor(defaultConfig?: APIOptions) {
    this.config = _.cloneDeep(defaultConfig || {});
  }

  request<Act extends keyof PCode, Res = PCode[Act]>(
    action: ActionOrActions<PCode>,
    requestParams?: RequestParams<PCode[Act]>
  ): ResponseType<Res> {
    requestParams = requestParams || {};
    let _config: APIOptions = _.cloneDeep(this.config);

    // 新增：检查是否为 MultiAPI 调用
    if (Array.isArray(action)) {
      return this.handleMultiAPI(action as Actions, _config);
    }

    if (requestParams.content) _config.apiType = "roa";
    //pop参数解析：requestParams中只有params时则解析为rpc接口，并展平requestParams
    const popTypeParams = requestParams?.content
      ? requestParams
      : requestParams.params;
    action = String(action) || "fakeAction";
    //是否为经过oneConsole上的控制台
    const isConsoleOnOne = window.ALIYUN_CONSOLE_CONFIG?.portalType === "one";
    let instance;
    if (_config.type === "mock") {
      updateConfig(_config, {
        headers: {
          "x-oneconsole-mock-enable": true,
        },
      });
      //    instance = axios.create();
      // instance.interceptors.request.use((config) =>
      //   customInterceptor(requestParams, config)
      // );
      // instance.interceptors.request.use((config) =>
      //   mockInterceptor(_config.productCode, action, config)

      // );
    } else if (_config.type === "atlas") {
      const isDebugMode = _config.atlasConfig?.debugMode;
      // 调试开发环境接口
      if (isDebugMode) {
        instance = getDevAtlasInstance(
          action,
          requestParams,
          _config.atlasConfig?.port
        );
      }
      //调试其他环境接口
      else {
        if (isConsoleOnOne) {
          //走oneConsole鉴权
          updateConfig(
            _config,
            atlasInterceptor(String(action), requestParams, _config)
          );
        } else {
          //走fecs免登
          instance = getFecsAtlasInstance(
            _config.productCode!,
            action,
            requestParams
          );
        }
      }
    } else if (_config.type === "pop") {
      if (!action) throw new Error("action is required. ");
      //适配pop接口
      const isPreEnv = window.location.host.indexOf("pre") > -1;
      if (!isConsoleOnOne) {
        if (!isPreEnv)
          return fetcher.callOpenApi(
            _config.productCode,
            String(action),
            popTypeParams,
            ...(_config.fecsConfig || {})
          );
        else {
          instance = axios.create({
            withCredentials: true,
          });
          instance.interceptors.request.use((config) =>
            preFecsOpenApiInterceptor(
              _config.productCode,
              action,
              requestParams,
              config
            )
          );
        }
      }
    } else if (_config.type === "custom") {
      //适配自定义接口
      instance = _config.createInstance?.() || axios.create({});
      instance.interceptors.request.use((config) =>
        customInterceptor(requestParams, config)
      );
    }

    let requestPromise = _config.isRisk
      ? callOpenApi<any, RequestParams<Act>>(
        _config.productCode,
        String(action) || "fakeAction",
        popTypeParams,
        {
          region: _config.region,
        }
      )
      : createService(
        _config.productCode!,
        String(action) || "fakeAction",
        _config,
        instance
      )(popTypeParams);

    return requestPromise.catch((err) => {
      const message = err.message;
      err = { ...err };
      if (_config.disableThrowResponseError)
        return {
          ...err,
          message,
          code: err.code || err.response?.status,
        };
      handleError({
        ...err,
        message,
        code: err.code || err.response?.status,
      });
    });
  }

  // 新增：MultiAPI 处理方法
  private handleMultiAPI<Res>(actions: Actions, _config: APIOptions): ResponseType<Res> {
    const isConsoleOnOne = window.ALIYUN_CONSOLE_CONFIG?.portalType === "one";
    let instance;

    // 根据不同类型设置 instance（复用现有逻辑）
    if (_config.type === "mock") {
      updateConfig(_config, {
        headers: { "x-oneconsole-mock-enable": true }
      });
    } else if (_config.type === "atlas") {
      const isDebugMode = _config.atlasConfig?.debugMode;
      if (isDebugMode) {
        // 对于 MultiAPI，我们使用第一个 action 来设置 instance
        instance = getDevAtlasInstance(
          actions[0]?.action || "multiAction",
          { params: actions },
          _config.atlasConfig?.port
        );
      } else {
        if (isConsoleOnOne) {
          updateConfig(
            _config,
            atlasInterceptor("multiAction", { params: actions }, _config)
          );
        } else {
          instance = getFecsAtlasInstance(
            _config.productCode!,
            "multiAction",
            { params: actions }
          );
        }
      }
    } else if (_config.type === "pop") {
      const isPreEnv = window.location.host.indexOf("pre") > -1;
      if (!isConsoleOnOne) {
        if (!isPreEnv) {
          return fetcher.callOpenApi(
            _config.productCode,
            "", // 空 action 触发 MultiAPI
            actions,
            ...(_config.fecsConfig || {})
          );
        } else {
          instance = axios.create({
            withCredentials: true,
          });
          instance.interceptors.request.use((config) =>
            preFecsOpenApiInterceptor(
              _config.productCode,
              "multiAction",
              { params: actions },
              config
            )
          );
        }
      }
    } else if (_config.type === "custom") {
      instance = _config.createInstance?.() || axios.create({});
      instance.interceptors.request.use((config) =>
        customInterceptor({ params: actions }, config)
      );
    }

    // 调用 createService 的 MultiAPI 功能
    const requestPromise = _config.isRisk
      ? callOpenApi<any, Actions>(
          _config.productCode,
          "", // 空 action 触发 MultiAPI
          actions,
          { region: _config.region }
        )
      : createService(
          _config.productCode!,
          "", // 空 action 触发 MultiAPI
          _config,
          instance
        )(actions);

    return requestPromise.catch((err) => {
      const message = err.message;
      err = { ...err };
      if (_config.disableThrowResponseError)
        return { ...err, message, code: err.code || err.response?.status };
      handleError({ ...err, message, code: err.code || err.response?.status });
    });
  }
}

function createInstance<PCode>(defaultConfig?: APIOptions) {
  const context = new ApiService<PCode>(defaultConfig);
  //@ts-ignore
  const instance: APIInstance<PCode> =
    ApiService.prototype.request.bind(context);
  Object.assign(instance, ApiService.prototype, context);
  instance.create = <PCode>(config?: APIOptions) => {
    return <PCode>createInstance(_.merge({}, defaultConfig, config)); //fix: 不要修改defaultConfig
    // return <PCode>createInstance(_.merge(context.config, config));
  };
  return instance;
}

export const apilot = createInstance();

export function getDevAtlasInstance(
  action: string,
  requestParams: any,
  atlasPort: string = "7001"
) {
  const instance = axios.create();
  instance.interceptors.request.use((config) =>
    devAtlasInterceptor(action, atlasPort, requestParams, config)
  );
  return instance;
}

export function getFecsAtlasInstance(
  product: string,
  action: string,
  requestParams: any
) {
  const instance = axios.create();
  instance.interceptors.request.use((config) =>
    fecsAtlasInterceptor(product, action, requestParams, config)
  );
  return instance;
}
