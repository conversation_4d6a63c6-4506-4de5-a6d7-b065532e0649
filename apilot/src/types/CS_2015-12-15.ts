
export interface HXWQDPEE{
  /**组件名称。 */
  name?: string;
  /**组件配置。 */
  config?: string;
  /**是否禁止默认安装，集群创建时除了安装必需组件外，还会额外安装一些日志组件等，您可以禁止这些默认行为，后续通过安装组件的API进行安装或者通过控制台安装。取值： 
- `true`：禁止默认安装。
- `false`：允许默认安装。
 */
  disabled?: boolean;
};
export interface HHNLVUIS{
  /**数据盘类型。取值： 
- `cloud`：普通云盘。
- `cloud_efficiency`：高效云盘。
- `cloud_ssd`：SSD云盘。
- `cloud_essd`：ESSD云盘。

默认值：`cloud_efficiency`。 */
  category?: string;
  /**数据盘大小，单位为GiB。 
取值范围：\[40,32768\]。

默认值：`120`。 */
  size?: number;
  /**是否对数据盘加密。取值： 
- `true`：对数据盘加密。
- `false`：不对数据盘加密。

默认值：`false`。 */
  encrypted?: string;
  /**选择自动快照策略ID，云盘会按照快照策略自动备份。 
默认值：空，不自动备份。 */
  auto_snapshot_policy_id?: string;
  /**节点数据盘磁盘性能等级，仅对ESSD磁盘生效。磁盘性能等级和磁盘大小有关。更多信息，请参见[ESSD云盘](~~122389~~)。 */
  performance_level?: string;
};
export interface XEGZXHMW{
  /**是否开启维护窗口。取值： - `true`：开启维护窗口。
- `false`：不开启维护窗口。

默认值：`false`。 */
  enable?: boolean;
  /**维护起始时间。Golang标准时间格式，例如15:04:05Z。 */
  maintenance_time?: string;
  /**维护时长。取值范围\[1,24\]，单位为小时。 
默认值：3h。 */
  duration?: string;
  /**维护周期，多个值用英文逗号（,）分隔。取值：{Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday} 
默认值：`Thursday`。 */
  weekly_period?: string;
};
export interface MUKSKOJM{
  /**容器运行时名称，ACK支持以下三种容器运行时。 
- `Sandboxed-Container.runv`：安全沙箱容器，提供更高的隔离性。
- `docker`。
- `containerd`。


默认：`docker`。 */
  name?: string;
  /**容器运行时版本，默认为最新版。 
关于安全沙箱运行时的变更详情，请参见[安全沙箱运行时变更记录](~~160312~~)。 */
  version?: string;
};
export interface UOSIJKAS{
  /**标签`key`值。 */
  key?: string;
  /**标签`value`值。 */
  value?: string;
};
export interface DAOFVASM{
  /**污点`key`值。 */
  key?: string;
  /**污点`value`值。 */
  value?: string;
  /**调度策略。取值： 
- `NoSchedule`：不能容忍，但仅影响调度过程，已被调度的Pod不受影响，仅对新增加的Pod生效。

- `NoExecute`：不能容忍，当污点变动时，Pod对象会被驱逐。

- `PreferNoSchedule`：柔性约束，节点现存Pod不受影响。尽量不去满足不合要求的Pod调度到节点上。

默认策略：`NoSchedule`。 */
  effect?: string;
};
export type Addon = HXWQDPEE;
export type DataDisk = HHNLVUIS;
export type MaintenanceWindow = XEGZXHMW;
export type Runtime = MUKSKOJM;
export type Tag = UOSIJKAS;
export type Taint = DAOFVASM;
export interface DescribeEventsResponseEventsData{
  /**事件等级。 */
  level?: string;
  /**事件状态。 */
  reason?: string;
  /**事件详情。 */
  message?: string;
};
export interface DescribeEventsResponseEvents{
  /**事件ID。 */
  event_id?: string;
  /**事件类型。取值： - `cluster_create`：创建集群。
- `cluster_scaleout`：扩容集群。
- `cluster_attach`：添加已有节点。
- `cluster_delete`：删除集群。
- `cluster_upgrade`：升级集群。
- `cluster_migrate`：迁移集群。
- `cluster_node_delete`：移除节点。
- `cluster_node_drain`：清空节点。
- `cluster_modify`：修改集群。
- `cluster_configuration_modify`：修改集群管控配置。
- `cluster_addon_install`：安装组件。
- `cluster_addon_upgrade`：升级组件。
- `cluster_addon_uninstall`：卸载组件。
- `runtime_upgrade`：升级运行时。
- `nodepool_upgrade`：升级节点池。
- `nodepool_update`：更新节点池。 */
  type?: string;
  /**事件来源。 */
  source?: string;
  /**事件主体。 */
  subject?: string;
  /**事件开始时间。 */
  time?: string;
  /**集群ID。 */
  cluster_id?: string;
  /**事件描述。 */
  data?: DescribeEventsResponseEventsData;
};
export interface DescribeEventsResponsePageInfo{
  /**每页显示数量。 */
  page_size?: number;
  /**分页查询页数。 */
  page_number?: number;
  /**结果总数。 */
  total_count?: number;
};
export interface DescribeEventsResponse{
  /**事件详情。 */
  events?: DescribeEventsResponseEvents[];
  /**分页信息。 */
  page_info?: DescribeEventsResponsePageInfo;
};
export interface StartAlertResponse{
  /**状态。 */
  status?: boolean;
  /**返回信息。 */
  msg?: string;
};
export interface StopAlertResponse{
  /**执行结果状态。 - True：执行成功。
- False：执行失败。 */
  status?: boolean;
  /**执行失败时返回信息提示。 */
  msg?: string;
};
export interface OpenAckServiceResponse{
  /**请求ID。 */
  request_id?: string;
  /**开通服务的订单号。 */
  order_id?: string;
};
export interface GrantPermissionsBody{
  /**授权目标集群ID。 
- 当`role_type`参数的值是`all-clusters`时，此参数的值传空字符串。 */
  cluster: string;
  /**该授权是否是自定义授权（`role_name`使用自定义的ClusterRole名称）。 */
  is_custom?: boolean;
  /**预置的角色名称，取值： 
- `admin`: 管理员。
- `ops`：运维人员。
- `dev`：开发人员。
- `restricted`: 受限用户。
- 自定义的ClusterRole名称。 */
  role_name: string;
  /**授权类型，取值： 
- `cluster`：集群维度。
- `namespace`: 命名空间维度。
- `all-clusters`: 所有集群维度。 */
  role_type: string;
  /**命名空间名称，集群维度授权时默认为空。 
 */
  namespace?: string;
  /**是否是RAM角色授权。 */
  is_ram_role?: boolean;
};
export interface DescribeUserPermissionResponse{
  /**集群访问配置，格式为： 
- 当是集群维度授权时，格式为：`{cluster_id}`。
- 当是命名空间维度授权时，格式为：`{cluster_id}/{namespace}`。
- 当是所有集群授权时，值固定为：`all-clusters`。 */
  resource_id?: string;
  /**授权类型，取值： 
- `cluster`：集群维度。
- `namespace`：命名空间维度。
- `console`：所有集群维度的授权（之前只用于控制台展示）。

 */
  resource_type?: string;
  /**自定义角色名称，当授权自定义角色时，该字段为指定的自定义集群管理角色名称。 */
  role_name?: string;
  /**预置的角色类型，取值： 
- `admin`：管理员。
- `ops`：运维人员。
- `dev`：开发人员。
- `restricted`：受限用户。
- `custom`：使用自定义的集群管理角色。 */
  role_type?: string;
  /**是否为集群创建者的授权，取值： 
- `0`：代表不是集群创建者的授权记录。
- `1`：代表该授权记录为集群创建者的管理员权限。 */
  is_owner?: number;
  /**是否为RAM角色授权，取值： 
- `0`：代表当前记录不是RAM角色授权。
- `1`：代表当前记录是RAM角色授权。

 */
  is_ram_role?: number;
};
export interface StartWorkflowBody{
  /**工作流类型，可选值：wgs或mapping。 */
  workflow_type: string;
  /**SLA类型，可选值：s、g、p。  
- 白银级（s）：超过90 Gbp的部分，按1.5 Gbp/min计算增加的时间。 
- 黄金级（g）：超过90 Gbp的部分，按2 Gbp/min计算增加的时间。 
- 铂金级（p）：超过90 Gbp的部分，按3 Gbp/min计算增加的时间。 */
  service?: string;
  /**mapping oss数据的存放region。 */
  mapping_oss_region?: string;
  /**mapping的第一个fastq文件名。 */
  mapping_fastq_first_filename?: string;
  /**mapping的第二个fastq文件名。 */
  mapping_fastq_second_filename?: string;
  /**存放mapping的bucket名称。 */
  mapping_bucket_name?: string;
  /**mapping的fastq文件路径。 */
  mapping_fastq_path?: string;
  /**mapping的reference文件位置。 */
  mapping_reference_path?: string;
  /**是否进行dup。 */
  mapping_is_mark_dup?: string;
  /**bam文件输出路径。 */
  mapping_bam_out_path?: string;
  /**bam文件输出名称。 */
  mapping_bam_out_filename?: string;
  /**wgs oss数据的存放region。 */
  wgs_oss_region?: string;
  /**wgs的第一个fastq文件名。 */
  wgs_fastq_first_filename?: string;
  /**wgs的第二个fastq文件名。 */
  wgs_fastq_second_filename?: string;
  /**存放wgs的bucket名称。 */
  wgs_bucket_name?: string;
  /**wgs的fastq文件路径。 */
  wgs_fastq_path?: string;
  /**wgs的reference文件路径。 */
  wgs_reference_path?: string;
  /**wgs的vcf输出路径。 */
  wgs_vcf_out_path?: string;
  /**wgs的vcf输出文件名称。 */
  wgs_vcf_out_filename?: string;
};
export interface StartWorkflowResponse{
  /**工作流名称 */
  JobName?: string;
};
export interface CancelWorkflowBody{
  /**执行的操作，目前只支持cancel。 */
  action: string;
};
export interface DescirbeWorkflowResponse{
  /**工作流创建时间。 */
  create_time?: string;
  /**工作流经过时长。 */
  duration?: string;
  /**任务结束时间。 */
  finish_time?: string;
  /**输入数据大小。 */
  input_data_size?: string;
  /**工作流名称。 */
  job_name?: string;
  /**工作流所在命名空间。 */
  job_namespace?: string;
  /**输出数据大小。 */
  output_data_size?: string;
  /**工作流当前状态。 */
  status?: string;
  /**碱基对个数。 */
  total_bases?: string;
  /**Reads个数。 */
  total_reads?: string;
  /**用户输入参数。 */
  user_input_data?: string;
};
export interface DescribeWorkflowsResponseJobs{
  /**集群ID。 */
  cluster_id?: string;
  /**工作流名称。 */
  job_name?: string;
  /**工作流创建时间。 */
  create_time?: string;
};
export interface DescribeWorkflowsResponse{
  /**Job列表。 */
  jobs?: DescribeWorkflowsResponseJobs[];
};
export interface CreateClusterBodyWorkerDataDisks{
  /**数据盘类型。 */
  category: string;
  /**是否对数据盘加密。取值： 
- `true`：对数据盘加密。
- `false`：不对数据盘加密。

默认值：`false`。 */
  encrypted?: string;
  /**数据盘大小，取值范围：40～32767。 */
  size: string;
  /**节点数据盘磁盘性能等级，仅对ESSD磁盘生效。磁盘性能等级和磁盘大小有关。更多信息，请参见[ESSD云盘](~~122389~~)。 */
  performance_level?: string;
};
export interface CreateClusterBody{
  /**集群名称。 
命名规则：由数字、汉字、英文字符或短划线（-）组成，长度范围1~63个字符，且不能以短划线（-）开头。 */
  name: string;
  /**集群所在的地域ID。 
 */
  region_id: string;
  /**集群类型。取值： 
- `Kubernetes`：专有版集群。
- `ManagedKubernetes`：标准托管版集群、边缘托管版集群。
- `Ask`：标准ASK集群。
- `ExternalKubernetes`：注册至ACK的外部集群。 */
  cluster_type: string;
  /**托管版集群类型，面向托管集群。取值： 
- `ack.pro.small`：专业托管集群，即："ACK Pro版集群"。
- `ack.standard`：标准托管集群。

默认值：`ack.standard`。取值可以为空，为空时则创建标准托管集群。

更多详情，请参见[ACK Pro版集群介绍](https://help.aliyun.com/document_detail/173290.html)。 */
  cluster_spec?: string;
  /**集群版本，与Kubernetes社区基线版本保持一致。建议选择最新版本，若不指定，默认使用最新版本。 
目前您可以在ACK控制台创建两种最新版本的集群。您可以通过API创建其他Kubernetes版本集群。关于ACK支持的Kubernetes版本，请参见[Kubernetes版本发布概览](~~185269~~)。 */
  kubernetes_version?: string;
  /**集群内容器运行时，默认采用Docker运行时，同时还支持Containerd和安全沙箱容器运行时。 
更多有关容器引擎的选择，请参见[如何选择Docker运行时与安全沙箱运行时](https://help.aliyun.com/document_detail/160313.html)。 */
  runtime?: Runtime;
  /**集群使用的专有网络，创建集群时必须为集群提供。 */
  vpcid: string;
  /**Pod虚拟交换机列表，您需要为每一个节点虚拟交换机指定至少一个相同可用区的Pod虚拟交换机并且不能跟节点`vswitch`重复，建议选择网段掩码不大于19的虚拟交换机。 
> 当集群采用Terway网络类型时，必须为集群指定`pod_vswitch_ids`。 */
  pod_vswitch_ids?: string[];
  /**Pod网络地址段，必须是有效的私有网段，即以下网段及其子网：10.0.0.0/8、172.16-31.0.0/12-16、192.168.0.0/16。不能与VPC及VPC内已有Kubernetes集群使用的网段重复，创建成功后不能修改。 
有关集群网络规划，请参见[VPC下 Kubernetes的网络地址段规划](~~86500~~)。

> 当创建Flannel网络类型的集群时，该字段为必填。 */
  container_cidr?: string;
  /**Service网络地址段，可选范围：10.0.0.0/16-24，172.16-31.0.0/16-24，192.168.0.0/16-24 不能与VPC网段10.1.0.0/21及VPC内已有Kubernetes集群使用的网段重复，创建成功后不能修改。

默认使用172.19.0.0/20网段。 */
  service_cidr: string;
  /**使用已有安全组创建集群时需要指定安全组ID，和`is_enterprise_security_group`二选一，集群节点自动加入到此安全组。 */
  security_group_id?: string;
  /**自动创建企业级安全组，当`security_group_id`为空的时生效。 
> 使用普通安全组时，集群内节点与Terway Pod数量之和不能超过2000。所以创建Terway网络类型集群时，建议使用企业安全组。

- `true`：创建并使用企业级安全组。
- `false`：不使用企业级安全组。

默认值：`true`。 */
  is_enterprise_security_group?: boolean;
  /**为专有网络配置SNAT。取值： 
- `true`：将为您创建NAT网关并自动配置SNAT规则，若您集群内的节点、应用等需要访问公网需要设置为`true`。
- `false`：不为您创建NAT网关及SNAT规则。这种模式下，集群内节点及应用将不能访问公网。

> 如果创建集群时未开启，后续业务需要访问公网，可手动开启。更多信息，请参见[手动开启](~~178480~~)。

默认值：`true`。 */
  snat_entry?: boolean;
  /**是否开启公网访问。通过EIP暴露API Server，实现集群公网访问。 
- `true`：开启公网访问。
- `false`：不开启公网访问。选择不开放时，则无法通过外网访问集群API Server。

默认值：`false`。 */
  endpoint_public_access?: boolean;
  /**是否开放公网SSH登录。用登录"专有版集群"的Master节点，托管版集群中该参数不生效。 - `true`：表示开放。
- `false`：表示不开放。

默认值：`false`。

 */
  ssh_flags?: boolean;
  /**集群使用的时区。 */
  timezone?: string;
  /**节点IP数量，通过指定网络的CIDR来确定IP的数量，只对于Flannel网络类型集群生效。 
默认值：`26`。 */
  node_cidr_mask?: string;
  /**自定义集群CA。 */
  user_ca?: string;
  /**自定义节点数据。 */
  user_data?: string;
  /**集群本地域名。 
命名规则：域名由小数点（.）分隔的一个或多个部分构成，每个部分最长为63个字符，可以使用小写字母、数字和短划线（-），且首尾必须为小写字母或数字。 */
  cluster_domain?: string;
  /**自定义节点名称。 
节点名称由三部分组成：前缀+节点IP地址子串+后缀：

- 前缀和后缀均可由英文句号（.）分隔的一个或多个部分构成，每个部分可以使用小写字母、数字和短划线（-），且首尾必须为小写字母和数字。
- IP地址段长度指截取节点IP地址末尾的位数，取值范围\[5,12\]。

例如，节点IP地址为：************，指定前缀为aliyun.com，IP地址段长度为5，后缀为test，则节点名称为aliyun.com00055test。 */
  node_name_mode?: string;
  /**自定义证书SAN，多个IP或域名以英文逗号（,）分隔。 */
  custom_san?: string;
  /**KMS密钥ID，使用该密钥对数据盘进行加密。更多详情，请参见[密钥管理服务](~~28935~~)。 
> 该功能只在专业托管版集群（ACK Pro版集群）中生效。

 */
  encryption_provider_key?: string;
  /**ServiceAccount是Pod和集群`apiserver`通讯的访问凭证。而`service-account-issuer`是`serviceaccount token`中的签发身份，即`token payload`中的`iss`字段。 
关于`ServiceAccount`更多详情，请参见[部署服务账户令牌卷投影](~~160384~~)。 */
  service_account_issuer?: string;
  /**ServiceAccount是Pod和集群`apiserver`通讯的访问凭证，而`api-audiences`是合法的请求`token`身份，用于`apiserver`服务端认证请求`token`是否合法。支持配置多个`audienc`e，通过英文逗号（,）分割。 
关于`ServiceAccount`更多详情，请参见[部署服务账户令牌卷投影](~~160384~~)。 */
  api_audiences?: string;
  /**节点自定义镜像，默认使用系统镜像。当选择自定义镜像时，将取代默认系统镜像。请参见[自定义镜像](~~146647~~)。 */
  image_id?: string;
  /**RDS实例列表，选择您想想要添加白名单的RDS实例。 建议前往RDS加入容器Pod网段与Node网段，设置RDS实例会由于实例非运行的状态导致无法弹出。 */
  rds_instances?: string[];
  /**节点标签。标签定义规则： 
- 标签由区分大小写的键值对组成，您最多可以设置20个标签。
- 标签键不可以重复，最长为64个字符；标签值可以为空，最长为128个字符。标签键和标签值都不能以“aliyun”、“acs:”、“https://”或“http://”开头。详情请参见[Labels and Selectors](https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#syntax-and-character-set)。 */
  tags?: Tag[];
  /**集群组件列表，创建集群时通过`addons`指定想要安装的集群组件。 
**网络组件**：必选，包含Flannel和Terway两种网络类型，创建集群时二选一：

- Flannel网络：\[{"name":"flannel","config":""}\]。
- Terway网络：\[{"name": "terway-eniip","config": ""}\] 。

**存储组件**：必选，支持`csi`和`flexvolume`两种类型：

- `csi`：\[{"name":"csi-plugin","config": ""},{"name": "csi-provisioner","config": ""}\]。
- `flexvolume`：\[{"name": "flexvolume","config": ""}\] 。

**日志组件**：可选。推荐开启，如果不开启日志服务时，将无法使用集群审计功能。

- 使用已有`SLS Project`：\[{"name": "logtail-ds","config": "{\"IngressDashboardEnabled\":\"true\",\"sls_project_name\":\"your_sls_project_name\"}"}\] 。
- 创建新的`SLS Project`：\[{"name": "logtail-ds","config": "{\"IngressDashboardEnabled\":\"true\"}"}\] 。

**Ingress组件**：可选，ACK专有版集群默认安装Ingress组件`nginx-ingress-controller`。

- 安装Ingress并且开启公网：\[{"name":"nginx-ingress-controller","config":"{\"IngressSlbNetworkType\":\"internet\"}"}\] 。
- 禁止默认安装Ingress：\[{"name": "nginx-ingress-controller","config": "","disabled": true}\] 。

**事件中心**：可选，默认开启。

事件中心提供对Kubernetes事件的存储、查询、告警等能力。Kubernetes事件中心关联的Logstore在90天内免费。关于免费策略的更多信息，请参见[创建并使用Kubernetes事件中心](https://help.aliyun.com/document_detail/150476.html#task-2389213)。

开启事件中心示例：\[{"name":"ack-node-problem-detector","config":"{\"sls_project_name\":\"your_sls_project_name\"}"}\]。 */
  addons?: Addon[];
  /**节点污点信息。污点和容忍度（Toleration）相互配合，可以用来避免Pod被分配到不合适的节点上。更多信息，请参见[taint-and-toleration](https://kubernetes.io/zh/docs/concepts/scheduling-eviction/taint-and-toleration/)。 */
  taints?: Taint[];
  /**集群是否安装云监控插件。取值： 
- `true`：安装云监控插件。
- `false`：不安装云监控插件。

默认值：`false`。

 */
  cloud_monitor_flags?: boolean;
  /**操作系统发行版。取值： 
- CentOS
- AliyunLinux
- QbootAliyunLinux
- Qboot
- Windows
- WindowsCore

默认值：`CentOS`。 */
  platform?: string;
  /**操作系统平台类型。取值： - Windows
- Linux

默认值：`Linux`。 */
  os_type?: string;
  /**等保加固。更多信息，请参见[ACK等保加固使用说明](~~196148~~)。 
取值：
- `true`：开启等保加固。
- `false`：不开启等保加固。

默认值：`false`。 */
  soc_enabled?: boolean;
  /**CIS安全加固。更多信息，请参见[ACK CIS加固使用说明](~~223744~~)。 
取值：

- `true`：开启CIS安全加固。
- `false`：不开启CIS安全加固。

默认值：`false`。 */
  cis_enabled?: boolean;
  /**节点CPU管理策略。当集群版本在1.12.6及以上时支持以下两种策略： 
- `static`：允许为节点上具有某些资源特征Pod增强其CPU亲和性和独占性。
- `none`：表示启用现有的默认CPU亲和性方案。

默认值：`none`。 */
  cpu_policy?: string;
  /**kube-proxy代理模式 
- `iptables`: 成熟稳定的kube-proxy代理模式，Kubernetes Service的服务发现和负载均衡使用iptables规则配置，但是性能一般，受规模影响较大，适用于集群存在少量的Service。
- `ipvs`：高性能的kube-proxy代理模式，Kubernetes Service的服务发现和负载均衡使用Linux IPVS模块进行配置，适用于集群存在大量的Service，对负载均衡有高性能要求的场景。

默认值：`ipvs`。 */
  proxy_mode?: string;
  /**节点服务端口，可选端口范围：\[30000,65535\]。 
默认值：`30000-32767`。 */
  node_port_range?: string;
  /**密钥对名称，和`login_password`二选一。 */
  key_pair: string;
  /**SSH登录密码，和`key_pair `二选一。密码规则为8~30个字符，且至少同时包含三项（大小写字母、数字和特殊符号）。 */
  login_password: string;
  /**Master节点数量，可选值`3`或`5`。 
默认值：`3`。 */
  master_count?: number;
  /**Master节点交换机ID列表，交换机个数取值范围为\[1,3\]。为确保集群的高可用性，推荐您选择3个交换机，且分布在不同的可用区。 
指定的实例规格数量需要和`master_count`保持一致，和`master_vswitch_ids`中的元素一一对应。 */
  master_vswitch_ids: string[];
  /**Master节点实例类型。更多信息，请参见[实例规格族](~~25378~~)。 */
  master_instance_types: string[];
  /**Master节点系统盘类型，取值： 
- `cloud_efficiency`：高效云盘。
- `cloud_ssd`：SSD云盘。
- `cloud_essd`：ESSD云盘。

默认值：`cloud_ssd`。不同可用区，默认值可能会有所差异。 */
  master_system_disk_category: string;
  /**Master节点系统盘类型，取值范围\[40,500\]，单位：GiB。 
默认值：`120`。 */
  master_system_disk_size: number;
  /**集群Master节点系统盘磁盘性能等级， 仅对ESSD磁盘生效。磁盘性能等级和磁盘大小有关，更多信息，请参见[ESSD云盘](~~122389~~)。 */
  master_system_disk_performance_level?: string;
  /**Master节点系统盘采用的自动快照策略ID。 */
  master_system_disk_snapshot_policy_id?: string;
  /**Master节点付费类型，取值： 
- `PrePaid`：包年包月。
- `PostPaid`：按量付费。

默认值：`PostPaid`。 */
  master_instance_charge_type?: string;
  /**Master节点付费周期，当付费类型为`PrePaid`的时候必须指定周期。 
取值：`Month`，当前仅支持以月为周期。 */
  master_period_unit?: string;
  /**Master节点包年包月时长，当`master_instance_charge_type`取值为`PrePaid`时才生效且为必选值。 
取值范围：{1, 2, 3, 6, 12, 24, 36, 48, 60}。

默认值：1。

 */
  master_period?: number;
  /**Master节点是否开启自动续费，当`master_instance_charge_type`取值为`PrePaid`时才生效，可选值为： 
- `true`：自动续费。
- `false`：不自动续费。

默认值：`true`。 */
  master_auto_renew?: boolean;
  /**Master节点自动续费周期，当选择包年包月付费类型时才生效，且为必选值。 
取值范围：{1, 2, 3, 6, 12}。

默认值：1。 */
  master_auto_renew_period?: number;
  /**Worker节点数。范围是\[0，100\]。 */
  num_of_nodes: number;
  /**集群节点所在虚拟交换机，当创建零节点的托管版集群时，该字段必填。 */
  vswitch_ids: string[];
  /**集群节点使用的虚拟交换机列表，一个节点对应一个值。 
当创建零节点的托管版集群时，字段`worker_vswitch_ids `非必填，但是需要提供`vswitch_ids`。 */
  worker_vswitch_ids: string[];
  /**Worker节点实例配置。 */
  worker_instance_types: string[];
  /**Worker节点系统盘类型。更多信息，请参见[块存储概述](~~63136~~)。 
取值：

- `cloud_efficiency`：高效云盘。
- `cloud_ssd`：SSD云盘。


默认值：`cloud_ssd`。 */
  worker_system_disk_category: string;
  /**Worker节点系统盘大小，单位为GiB。 
取值范围：\[40,500\]。

该参数的取值必须大于或者等于max{40, ImageSize}。

默认值：`120`。 */
  worker_system_disk_size: number;
  /**Worker节点系统盘采用的自动快照策略ID。 */
  worker_system_disk_snapshot_policy_id?: string;
  /**当系统盘为ESSD云盘时，可设置ESSD云盘的性能级别PL（Performance Level）。更多信息，请参见[ESSD云盘](~~122389~~)。 
取值：

- PL0
- PL1
- PL2
- PL3 */
  worker_system_disk_performance_level?: string;
  /**Worker节点数据盘类型、大小等配置的组合。 */
  worker_data_disks?: CreateClusterBodyWorkerDataDisks[];
  /**Worker节点付费类型，取值： 
- `PrePaid`：包年包月。
- `PostPaid`：按量付费。

默认值：按量付费。 */
  worker_instance_charge_type?: string;
  /**Wroker节点付费周期，当付费类型为`PrePaid`的时候需要指定周期。 
取值：`Month`，当前只支持以月为周期。 */
  worker_period_unit?: string;
  /**Worker节点包年包月时长，当`worker_instance_charge_type`取值为`PrePaid`时才生效且为必选值。 
取值范围：{1, 2, 3, 6, 12, 24, 36, 48, 60}。

默认值：1。 */
  worker_period?: number;
  /**Worker节点是否开启自动续费，当`worker_instance_charge_type`取值为`PrePaid`时才生效，取值： 
- `true`：自动续费。
- `false`：不自动续费。

默认值：`true`。 */
  worker_auto_renew?: boolean;
  /**Worker节点自动续费周期，当选择包年包月付费类型时才生效，且为必选值。 
取值范围：{1, 2, 3, 6, 12}。 */
  worker_auto_renew_period?: number;
  /**使用已经节点创建集群时，需要指定ECS实例列表，该实例会作为Worker节点加入集群。 
> 使用已有实例创建集群时，该字段必填。 */
  instances?: string[];
  /**使用已有实例创建集群时，是否对实例进行数据盘挂载，取值： 
- `true`：将容器和镜像存储在数据盘，数据盘内原有数据将丢失，请注意备份数据。

- `false`：不将容器和镜像存储在数据盘。

默认值：`false`。

数据盘挂载规则：

- 如果ECS已挂载数据盘，且最后一块数据盘的文件系统未初始化，系统会自动将该数据盘格式化为ext4，用来存放内容/var/lib/docker、/var/lib/kubelet。
- 如果ECS未挂载数据盘，则不会挂载新的数据盘。 */
  format_disk?: boolean;
  /**使用已有实例创建集群时，是否保留实例名称。 
- `true`：保留。
- `false`：不保留，会用系统规则进行替换。

默认值：`true`。 */
  keep_instance_name?: boolean;
  /**集群内服务发现类型，用于在`ASK`集群中指定服务发现方式。 
- `CoreDNS`：使用Kubernetes原生标准服务发现组件CoreDNS，需要在集群部署一组容器用于DNS解析。默认采用两个0.25 Core 512 MiB规格的ECI实例。
- `PrivateZone`：使用阿里云PrivateZone产品提供服务发现能力，需要开启PrivateZone服务。

默认值：不开启。 */
  service_discovery_types?: string[];
  /**创建ASK集群时，是否在VPC中创建NAT网关并配置SNAT规则。取值： 
- `true`：将为您创建NAT网关并自动配置SNAT规则，集群VPC将具备公网访问能力。
- `false`：不为您创建NAT网关及SNAT规则。集群VPC将不具备公网访问能力。

默认值：`false`。 */
  nat_gateway?: boolean;
  /**集群所属地域的可用区ID。此参数为ASK集群特有参数。 
当创建ASK集群时，如果未指定`vpc_id`和`vswitch_ids`，必须为集群指定`zone_id`，用于自动在该可用区创建VPC网络资源。

 */
  zone_id?: string;
  /**边缘集群标识。当创建集群类型为边缘托管版时，该参数必填且取值必须为`Edge`。 
- `Default`：非边缘集群。

- `Edge`：边缘集群。 */
  profile?: string;
  /**集群开启日志服务，只针对ASK集群生效，且取值必须是`SLS`。 */
  logging_type?: string;
  /**控制平面组件日志保存天数。 */
  controlplane_log_ttl?: string;
  /**控制平面组件日志服务Project，可以使用已有Project用于日志存储，也可以使用系统自动创建Project用户日志存储。如果选择自动创建日志服务Project将会自动创建一个名称为`k8s-log-{ClusterID}`的日志服务Project。 */
  controlplane_log_project?: string;
  /**组件名称列表，指定那些控制平面的组件日志需要被收集。 
默认采集apiserver、kcm、scheduler组件的日志。 */
  controlplane_log_components?: string[];
  /**集群删除保护，防止通过控制台或API误删除集群。取值： 
- `true`：启用集群删除保护，将不能通过控制台或API删除集群。
- `false`：不启用集群删除保护，则能通过控制台或API删除集群。

默认值：`false`。 */
  deletion_protection?: boolean;
  /**集群创建失败是否回滚。取值： 
- `true`：当集群创建失败时，进行回滚操作。
- `false`：当集群创建失败时，不进行回滚操作。


默认值：`true`。 */
  disable_rollback?: boolean;
  /**集群创建超时时间，单位分钟。 
默认值：`60`。 */
  timeout_mins?: number;
  /**操作系统发行版类型，推荐使用该字段指定节点操作系统。取值： 
- CentOS
- AliyunLinux
- AliyunLinux Qboot
- AliyunLinuxUEFI
- AliyunLinux3
- Windows
- WindowsCore
- AliyunLinux3Arm64
- ContainerOS

默认值：`CentOS`。 */
  image_type?: string;
  /**负载均衡规格，取值： - slb.s1.small
- slb.s2.small
- slb.s2.medium
- slb.s3.small
- slb.s3.medium
- slb.s3.large

默认值：`slb.s2.small`。 */
  load_balancer_spec?: string;
  /**是否启用RRSA功能。 */
  enable_rrsa?: boolean;
  /**集群所属资源组ID，实现不同资源的隔离。 */
  resource_group_id?: string;
  /**付费类型。 */
  charge_type?: string;
  /**包年包月时间单位。 */
  period_unit?: string;
  /**包年包月时间。 */
  period?: number;
  /**集群IP Stack */
  ip_stack?: string;
};
export interface CreateClusterResponse{
  /**集群ID。 */
  cluster_id?: string;
  /**请求ID。 */
  request_id?: string;
  /**任务ID。 */
  task_id?: string;
};
export interface DeleteClusterResponse{
  /**任务ID。 */
  task_id?: string;
};
export interface ScaleOutClusterBodyWorkerDataDisks{
  /**数据盘类型。 */
  category?: string;
  /**数据盘大小，取值范围：40～32767。 */
  size?: string;
  /**是否对数据盘加密。取值： 
- `true`：对数据盘加密。
- `false`：不对数据盘加密。

默认值：`false`。 */
  encrypted?: string;
  /**选择自动快照策略ID，云盘会按照快照策略自动备份。 
默认值：空，不自动备份。 */
  auto_snapshot_policy_id?: string;
};
export interface ScaleOutClusterBody{
  /**扩容节点数量。 */
  count: number;
  /**密钥对名称，和`login_password`二选一。 */
  key_pair: string;
  /**SSH登录密码，和`key_pair `二选一。密码规则为8~30个字符，且至少同时包含三项（大小写字母、数字和特殊符号）。 */
  login_password: string;
  /**虚拟交换机ID，创建集群时可选择1~3个虚拟交换机。为保证集群高可用，建议选择不同可用区的虚拟交换机。 */
  vswitch_ids: string[];
  /**Worker节点付费类型，取值： 
- `PrePaid`：包年包月。
- `PostPaid`：按量付费。

默认值：`PostPaid`。 */
  worker_instance_charge_type?: string;
  /**Worker节点包年包月时长，当`worker_instance_charge_type`取值为`PrePaid`时才生效且为必选值。 
取值范围：{1, 2, 3, 6, 12, 24, 36, 48, 60}。

默认值：1。 */
  worker_period?: number;
  /**Wroker节点付费周期，当付费类型为`PrePaid`的时需要指定周期。 
取值：`Month`，当前只支持以月为周期。 */
  worker_period_unit?: string;
  /**Worker节点是否开启自动续费，当`worker_instance_charge_type`取值为`PrePaid`时才生效，取值： 
- `true`：自动续费。
- `false`：不自动续费。

默认值：`true`。 */
  worker_auto_renew?: boolean;
  /**Worker节点自动续费周期，当选择包年包月付费类型时才生效，且为必选值。 
取值范围：{1, 2, 3, 6, 12}。

默认值：`1`。 */
  worker_auto_renew_period?: number;
  /**Worker节点实例配置。 */
  worker_instance_types: string[];
  /**Worker节点系统盘类型，取值： 
- `cloud_efficiency`：高效云盘。
- ` cloud_ssd`：SSD云盘。
- ` cloud_essd`：ESSD云盘。

默认值：`cloud_ssd`。 */
  worker_system_disk_category: string;
  /**Worker节点系统盘大小，单位为GiB。 
取值范围：\[40,500\]。

默认值：`120`。 */
  worker_system_disk_size: number;
  /**Worker节点数据盘类型、大小等配置的组合。 */
  worker_data_disks?: ScaleOutClusterBodyWorkerDataDisks[];
  /**集群是否安装云监控插件。取值： 
- `true`：安装云监控插件。
- `false`：不安装云监控插件。

默认值：`false`。
 */
  cloud_monitor_flags?: boolean;
  /**节点CPU管理策略。当集群版本在1.12.6及以上时支持以下两种策略： 
- `static`：允许为节点上具有某些资源特征Pod增强其CPU亲和性和独占性。
- `none`：表示启用现有的默认CPU亲和性方案。

默认值：`none`。 */
  cpu_policy?: string;
  /**节点自定义镜像，默认使用系统镜像。当选择自定义镜像时，将取代默认系统镜像。请参见[自定义镜像](~~146647~~)。 */
  image_id?: string;
  /**节点池自定义数据。更多详情，请参见[生成实例自定义数据](~~49121~~)。 */
  user_data?: string;
  /**如果指定了RDS实例列表，集群节点ECS会自动加入RDS访问白名单。 */
  rds_instances?: string[];
  /**节点标签。标签定义规则： 
- 标签由区分大小写的键值对组成，您最多可以设置20个标签。
- 标签键不可以重复，最长为64个字符；标签值可以为空，最长为128个字符。标签键和标签值都不能以“aliyun”、“acs:”、“https://”或“http://”开头。详情请参见[Labels and Selectors](https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#syntax-and-character-set)。 */
  tags?: Tag[];
  /**节点污点信息。污点和容忍度（Toleration）相互配合，可以用来避免Pod被分配到不合适的节点上。更多信息，请参见[taint-and-toleration](https://kubernetes.io/zh/docs/concepts/scheduling-eviction/taint-and-toleration/)。 */
  taints?: Taint[];
  /**集群内容器运行时。 */
  runtime?: Runtime;
};
export interface ScaleOutClusterResponse{
  /**集群ID。 */
  cluster_id?: string;
  /**请求ID。 */
  request_id?: string;
  /**任务ID。 */
  task_id?: string;
};
export interface ModifyClusterBody{
  /**集群是否绑定EIP，用于公网访问API Server。取值： 
- `true`：集群绑定EIP。
- `false`：集群不绑定EIP。 */
  api_server_eip?: boolean;
  /**集群API Server绑定的EIP实例ID，仅当`api_server_eip`取值为`true`时生效。 */
  api_server_eip_id?: string;
  /**集群删除保护，防止通过控制台或API误删除集群。取值： 
- `true`：启用集群删除保护，将不能通过控制台或API删除集群。
- `false`：不启用集群删除保护，则能通过控制台或API删除集群。

默认值：`false`。 */
  deletion_protection?: boolean;
  /**实例删除保护，防止通过控制台或API误删除释放节点。取值： 
- `true`: 不能通过控制台或API误删除集群。
- `false`：能通过控制台或API误删除集群。

默认值：`false`。 */
  instance_deletion_protection?: boolean;
  /**重新绑定集群测试域名。取值： 
- `true`：重新绑定集群测试域名。
- `false`：不重新绑定集群测试域名。

默认值：`false`。 */
  ingress_domain_rebinding?: string;
  /**被修改集群的SLB实例ID。 */
  ingress_loadbalancer_id?: string;
  /**集群资源组ID。 */
  resource_group_id?: string;
  /**集群维护窗口，该功能只在Pro托管版集群中生效。 */
  maintenance_window?: MaintenanceWindow;
  /**启用或禁用RRSA功能。取值： 
- `true`：启用。

- `false`：禁用。 */
  enable_rrsa?: boolean;
};
export interface ModifyClusterResponse{
  /**集群ID。 */
  cluster_id?: string;
  /**请求ID。 */
  request_id?: string;
  /**任务ID。 */
  task_id?: string;
};
export interface MigrateClusterBody{
  /**Bucket名称。 */
  oss_bucket_name?: string;
  /**Bucket访问端点。 */
  oss_bucket_endpoint?: string;
};
export interface MigrateClusterResponse{
  /**集群ID。 */
  cluster_id?: string;
  /**请求ID。 */
  request_id?: string;
  /**任务ID。 */
  task_id?: string;
};
export interface ScaleClusterBodyTags{
  key?: string;
};
export interface ScaleClusterBodyTaints{
  effect?: string;
  key?: string;
  value?: string;
};
export interface ScaleClusterBodyWorkerDataDisks{
  category?: string;
  encrypted?: string;
  size?: string;
};
export interface ScaleClusterBody{
  cloud_monitor_flags?: boolean;
  count?: number;
  cpu_policy?: string;
  disable_rollback?: boolean;
  key_pair?: string;
  login_password?: string;
  tags?: ScaleClusterBodyTags[];
  taints?: ScaleClusterBodyTaints[];
  vswitch_ids?: string[];
  worker_auto_renew?: boolean;
  worker_auto_renew_period?: number;
  worker_data_disk?: boolean;
  worker_data_disks?: ScaleClusterBodyWorkerDataDisks[];
  worker_instance_charge_type?: string;
  worker_instance_types?: string[];
  worker_period?: number;
  worker_period_unit?: string;
  worker_system_disk_category?: string;
  worker_system_disk_size?: number;
};
export interface ScaleClusterResponse{
  cluster_id?: string;
  request_id?: string;
  task_id?: string;
};
export interface UpdateK8sClusterUserConfigExpireBody{
  /**用户指定的过期时间。单位：小时。 >expire_hour过期小时数需大于0且小于等于 876000小时（100年）。 */
  expire_hour: number;
  /**用户ID。 */
  user: string;
};
export interface DescribeClusterResourcesResponse{
  /**集群ID。 */
  cluster_id?: string;
  /**资源创建时间。 */
  created?: string;
  /**资源ID。 */
  instance_id?: string;
  /**资源信息。关于资源的源信息，请参见[ListStackResources](~~133836~~)。 */
  resource_info?: string;
  /**资源类型。 */
  resource_type?: string;
  /**资源状态。可选值： 
- `CREATE_COMPLETE`：成功创建资源。
- `CREATE_FAILED`：创建资源失败。
- `CREATE_IN_PROGRESS`：创建资源中。
- `DELETE_FAILED`：删除资源失败。
- `DELETE_IN_PROGRESS`：删除资源中。
- `ROLLBACK_COMPLETE`：成功回滚。
- `ROLLBACK_FAILED`：回滚失败。
- `ROLLBACK_IN_PROGRESS`：回滚中。 */
  state?: string;
  /**资源是否由ACK创建： 
- 1：表示由ACK创建
- 0：表示该资源为已有资源 */
  auto_create?: number;
};
export interface DescribeClusterDetailResponse{
  /**集群ID。 */
  cluster_id?: string;
  /**集群类型，取值： 
- `Kubernetes`: 专有版集群。
- `ManagedKubernetes`：托管版集群。
- `Ask`：ASK集群。
- `ExternalKubernetes`：注册集群。 */
  cluster_type?: string;
  /**集群创建时间。 */
  created?: string;
  /**集群初始化版本。 */
  init_version?: string;
  /**集群当前版本。ACK支持的Kubernetes版本，请参见[Kubernetes版本发布概览](~~185269~~)。 */
  current_version?: string;
  /**集群可升级版本。 */
  next_version?: string;
  /**集群删除保护，防止通过控制台或API误删除集群。取值： 
- `true`：启用集群删除保护，将不能通过控制台或API删除集群。
- `false`：不启用集群删除保护，则能通过控制台或API删除集群。

 */
  deletion_protection?: boolean;
  /**集群中的Docker版本。 */
  docker_version?: string;
  /**集群Ingress SLB实例ID。 */
  external_loadbalancer_id?: string;
  /**集群元数据信息。 */
  meta_data?: string;
  /**集群名称。 
命名规则：由数字、汉字、英文字符或短划线（-）组成，长度范围1~63个字符，且不能以短划线（-）开头。 */
  name?: string;
  /**集群网络模式，取值： 
- `classic`：经典网络。
- `vpc`：专有网络。
- `overlay`：叠加网络。
- `calico`：Calico网络。

默认值：`vpc`。 */
  network_mode?: string;
  /**集群所在地域ID。 */
  region_id?: string;
  /**集群资源组ID。 */
  resource_group_id?: string;
  /**集群安全组ID。 */
  security_group_id?: string;
  /**集群节点数，包含Master节点及Worker节点。 */
  size?: number;
  /**集群运行状态，取值： 
- `initial`：集群创建中。
- `failed`：集群创建失败。
- `running`：集群运行中。
- `updating`：集群升级中。
- `updating_failed`：集群升级失败。
- `scaling`：集群伸缩中。
- `waiting`：等待接入状态。
- `disconnected`：断开状态。
- `stopped`：集群已经停止运行。
- `deleting`：集群删除中。
- `deleted`：集群已经被删除。
- `delete_failed`：集群删除失败。    */
  state?: string;
  /**集群资源标签。 */
  tags?: Tag[];
  /**集群更新时间。 */
  updated?: string;
  /**集群专有网络ID，创建集群时的必填参数。 */
  vpc_id?: string;
  /**虚拟交换机ID，创建集群时可选择1~3个虚拟交换机。为保证集群高可用，建议选择不同可用区的虚拟交换机。 */
  vswitch_id?: string;
  /**Pod网络地址段，必须是有效的私有网段，即以下网段及其子网： - 10.0.0.0/8
- 172.16-31.0.0/12-16
- 192.168.0.0/16

不能与VPC及VPC内已有Kubernetes集群使用的网段重复，创建成功后不能被修改。

关于集群网络规划，请参见[VPC下Kubernetes的网络地址段规划](~~186964~~)。 */
  subnet_cidr?: string;
  /**集群所在可用区ID。 */
  zone_id?: string;
  /**集群访问地址，包含内网访问地址及公网访问地址。 */
  master_url?: string;
  /**集群Private Zone配置。 
- `true`：启用Private Zone。
- `false`：不启用Private Zone。
 */
  private_zone?: boolean;
  /**面向场景时的集群类型： 
- `Default`：非边缘场景集群。
- `Edge`：边缘场景集群。

 */
  profile?: string;
  /**托管版集群类型，面向托管集群： 
- `ack.pro.small`：专业托管集群。
- `ack.standard` ：标准托管集群。

 */
  cluster_spec?: string;
  /**Worker RAM角色名称，授权ECS实例为集群Woker节点。 */
  worker_ram_role_name?: string;
  /**集群维护窗口配置，只在专业托管版本（即：ACK Pro版集群）中生效。 */
  maintenance_window?: MaintenanceWindow;
  /**集群ROS参数集合。 */
  parameters?: any;
};
export interface DescribeUserQuotaResponseEdgeImprovedNodepoolQuota{
  /**每个边缘增强型节点池允许的最大带宽，单位：Mbps。 */
  bandwidth?: number;
  /**每个账号下允许创建的边缘增强型节点池数量。 */
  count?: number;
  /**每个边缘增强型节点池最大购买时长，单位：月。 >由于边缘增强型节点池为按量付费，您暂时无需关注该字段。 */
  period?: number;
};
export interface DescribeUserQuotaResponse{
  /**托管版集群配额。默认为20。如果超过默认值，需[到配额平台提交申请](https://quotas.console.aliyun.com/products/csk/quotas)扩容。 */
  amk_cluster_quota?: number;
  /**ASK集群配额，默认为20。如果超过默认值，需[到配额平台提交申请](https://quotas.console.aliyun.com/products/csk/quotas)扩容。 */
  ask_cluster_quota?: number;
  /**单集群节点池配额，默认值为20。如果超过默认值，需[到配额平台提交申请](https://quotas.console.aliyun.com/products/csk/quotas)扩容 */
  cluster_nodepool_quota?: number;
  /**单账户总集群配额，默认为50。如果超过默认值，需[到配额平台提交申请](https://quotas.console.aliyun.com/products/csk/quotas)扩容。 */
  cluster_quota?: number;
  /**单集群节点数配额，默认为100。如果超过默认值，需[到配额平台提交申请](https://quotas.console.aliyun.com/products/csk/quotas)扩容。 */
  node_quota?: number;
  /**边缘增强型节点池Quota。 */
  edge_improved_nodepool_quota?: DescribeUserQuotaResponseEdgeImprovedNodepoolQuota;
  /**新配额项，如存在该字段，则以该字段为准。 */
  quotas?: any;
};
export interface DescribeClustersV1ResponseClusters{
  /**集群ID。 */
  cluster_id?: string;
  /**集群类型，取值： 
- `Kubernetes`: 专有版集群。
- `ManagedKubernetes`：托管版集群。
- `Ask`：ASK集群。
- `ExternalKubernetes`：注册集群。
 */
  cluster_type?: string;
  /**集群创建时间。 */
  created?: string;
  /**集群版本，与Kubernetes社区基线版本保持一致。建议选择最新版本，若不指定，默认使用最新版本。 
目前ACK控制台提供两种最新Kubernetes版本的集群。您可以通过API创建其他Kubernetes版本集群。ACK支持的Kubernetes版本，请参见[Kubernetes版本发布概览](~~185269~~)。 */
  init_version?: string;
  /**集群当前版本。 */
  current_version?: string;
  /**集群可升级版本。 */
  next_version?: string;
  /**集群删除保护，防止通过控制台或API误删除集群。取值： 
- `true`：启用集群删除保护，将不能通过控制台或API删除集群。
- `false`：不启用集群删除保护，则能通过控制台或API删除集群。
 */
  deletion_protection?: boolean;
  /**集群Docker版本。 */
  docker_version?: string;
  /**集群中Ingress SLB实例。 
默认实例规格：性能保障型（slb.s1.small）。 */
  external_loadbalancer_id?: string;
  /**集群API Server访问地址，包含内网访问地址以及公网访问地址。 */
  master_url?: string;
  /**集群元数据信息。 */
  meta_data?: string;
  /**集群名称。 
命名规则：由数字、汉字、英文字符或短划线（-）组成，长度范围1~63个字符，且不能以短划线（-）开头。 */
  name?: string;
  /**集群网络模式。取值： 
- `classic`：经典网络。
- `vpc`：专有网络。
- `overlay`：叠加网络。
- `calico`：Calico网络。
 */
  network_mode?: string;
  /**集群Private Zone配置。取值： 
- `true`：启用Private Zone。
- `false`：不启用Private Zone。
 */
  private_zone?: boolean;
  /**集群标识，取值： 
- `Edge`：边缘托管版集群。
- `Default`：非边缘托管版集群。

 */
  profile?: string;
  /**集群所在地域ID。 */
  region_id?: string;
  /**集群资源组ID。 */
  resource_group_id?: string;
  /**集群安全组ID。 */
  security_group_id?: string;
  /**集群当前节点数量，包含Master节点以及Worker节点。 */
  size?: number;
  /**集群运行状态，取值： 
- `initial`：集群创建中。
- `failed`：集群创建失败。
- `running`：集群运行中。
- `updating`：集群升级中。
- ` updating_failed`：集群升级失败。
- `scaling`：集群伸缩中。
- `stopped`：集群已经停止运行。
- `deleting`：集群删除中。
- `deleted`：集群已经被删除。
- `delete_failed`：集群删除失败。    */
  state?: string;
  /**Pod网络地址段，必须是有效的私有网段，即以下网段及其子网： - 10.0.0.0/8
- 172.16-31.0.0/12-16
- 192.168.0.0/16

不能与VPC及VPC内已有Kubernetes集群使用的网段重复，创建成功后不能修改。

关于集群网络规划，请参见[VPC下Kubernetes的网络地址段规划](~~86500~~)。 */
  subnet_cidr?: string;
  /**集群资源标签。 */
  tags?: Tag[];
  /**集群更新时间。 */
  updated?: string;
  /**集群专有网络ID，创建集群时必传。 */
  vpc_id?: string;
  /**虚拟交换机ID，创建集群时可选择1~3个虚拟交换机。为保证集群高可用，建议选择不同可用区的虚拟交换机。 */
  vswitch_id?: string;
  /**Worker RAM角色名称，授权ECS实例为集群Woker节点。 */
  worker_ram_role_name?: string;
  /**集群所属可用区ID。 */
  zone_id?: string;
  /**托管版集群类型，面向托管集群： - `ack.pro.small`：专业托管集群。
- `ack.standard`：标准托管集群。

 */
  cluster_spec?: string;
  /**集群维护窗口，该功能只在专业托管版中生效。 */
  maintenance_window?: MaintenanceWindow;
};
export interface DescribeClustersV1ResponsePageInfo{
  /**分页数。 */
  page_number?: number;
  /**单页大小。 */
  page_size?: number;
  /**结果总数。 */
  total_count?: number;
};
export interface DescribeClustersV1Response{
  /**集群信息列表。 */
  clusters?: DescribeClustersV1ResponseClusters[];
  /**分页信息。  */
  page_info?: DescribeClustersV1ResponsePageInfo;
};
export interface DescribeExternalAgentResponse{
  /**YAML格式的代理配置。 */
  config?: string;
};
export interface DescribeClusterLogsResponse{
  /**日志ID。 */
  ID?: number;
  /**集群ID。 */
  cluster_id?: string;
  /**日志内容。 */
  cluster_log?: string;
  /**日志产生时间。 */
  created?: string;
  /**日志更新时间。 */
  updated?: string;
};
export interface DescribeTaskInfoResponseTarget{
  /**任务执行对象ID。 */
  id?: string;
  /**任务执行对象类型。 */
  type?: string;
};
export interface DescribeTaskInfoResponseStages{
  /**任务阶段状态。 */
  state?: string;
  /**任务阶段开始时间。 */
  start_time?: string;
  /**任务阶段结束时间。 */
  end_time?: string;
  /**任务阶段信息。 */
  message?: string;
  /**任务阶段输出。 */
  outputs?: any;
};
export interface DescribeTaskInfoResponseEvents{
  /**事件动作。 */
  action?: string;
  /**事件等级。 */
  level?: string;
  /**事件消息。 */
  message?: string;
  /**事件原因。 */
  reason?: string;
  /**事件来源。 */
  source?: string;
  /**事件生成时间。 */
  timestamp?: string;
};
export interface DescribeTaskInfoResponseTaskResult{
  /**任务操作的资源，例如：扩容时操作资源是实例，那么此处就是实例ID。 */
  data?: string;
  /**资源扩容的状态。取值： - `success`：扩容成功。
- `failed`：扩容失败。
- `initial`：初始化中。 */
  status?: string;
};
export interface DescribeTaskInfoResponseError{
  /**错误码。 */
  code?: string;
  /**错误消息。 */
  message?: string;
};
export interface DescribeTaskInfoResponse{
  /**任务ID。 */
  task_id?: string;
  /**集群ID。 */
  cluster_id?: string;
  /**任务类型，扩容任务的类型为`cluster_scaleout`。 */
  task_type?: string;
  /**代表任务的运行状态。取值： - `running`：任务正在运行中。
- `fail`：任务运行失败。
- `success`：任务运行完成。
 */
  state?: string;
  /**任务创建时间。 */
  created?: string;
  /**任务更新时间。 */
  updated?: string;
  /**任务执行对象。 */
  target?: DescribeTaskInfoResponseTarget;
  /**任务参数。 */
  parameters?: any;
  /**任务阶段。 */
  stages?: DescribeTaskInfoResponseStages[];
  /**任务当前运行阶段。 */
  current_stage?: string;
  /**任务产生的事件。 */
  events?: DescribeTaskInfoResponseEvents[];
  /**任务执行详情。 */
  task_result?: DescribeTaskInfoResponseTaskResult[];
  /**任务错误信息。 */
  error?: DescribeTaskInfoResponseError;
};
export interface DescribeKubernetesVersionMetadataResponseImages{
  /**镜像ID。 */
  image_id?: string;
  /**镜像名称。 */
  image_name?: string;
  /**操作系统平台。取值： - `AliyunLinux`
- `CentOS`
- `Windows`
- `WindowsCore` */
  platform?: string;
  /**镜像版本。 */
  os_version?: string;
  /**操作系统发行版类型，推荐使用该字段指定节点操作系统。取值： 
-  `CentOS`
- `AliyunLinux`
-  `AliyunLinux Qboot`
- `AliyunLinuxUEFI`
- `AliyunLinux3`
- `Windows`
- `WindowsCore`
- `AliyunLinux3Arm64`
- `ContainerOS` */
  image_type?: string;
  /**操作系统类型，例如： 
- `Windows`
- `Linux` */
  os_type?: string;
  /**镜像分类，部分取值： 
- `system`：公共镜像。
- `self`：您的自定义镜像。
- `others`：其他用户的公开镜像。
- `marketplace`：镜像市场镜像。
 */
  image_category?: string;
  /**镜像架构。 */
  architecture?: string;
};
export interface DescribeKubernetesVersionMetadataResponse{
  /**Kubernetes版本功能特性。 */
  capabilities?: any;
  /**系统镜像列表。	 */
  images?: DescribeKubernetesVersionMetadataResponseImages[];
  /**Kubernetes版本元数据信息。	 */
  meta_data?: any;
  /**容器运行时配置。	 */
  runtimes?: Runtime[];
  /**ACK发布的Kubernetes版本。更多版本信息，请参见[Kubernetes版本发布概览](~~185269~~)。 */
  version?: string;
  /**Kubernetes版本发布时间。 */
  release_date?: string;
  /**Kubernetes版本过期时间。	 */
  expiration_date?: string;
  /**Kubernetes版本是否可创建。 */
  creatable?: boolean;
};
export interface DescribeClusterUserKubeconfigResponse{
  /**集群访问配置。关于如何查看访问集群配置信息，请参见[配置集群凭据](~~86494~~)。 */
  config?: string;
  /**kubeconfig的过期时间。格式：RFC3339格式的UTC时间。 */
  expiration?: string;
};
export interface DescribeClustersResponseTags{
  key?: string;
  value?: string;
};
export interface DescribeClustersResponse{
  cluster_id?: string;
  cluster_type?: string;
  created?: string;
  current_version?: string;
  data_disk_category?: string;
  data_disk_size?: number;
  deletion_protection?: boolean;
  docker_version?: string;
  external_loadbalancer_id?: string;
  init_version?: string;
  master_url?: string;
  meta_data?: string;
  name?: string;
  network_mode?: string;
  private_zone?: boolean;
  profile?: string;
  region_id?: string;
  resource_group_id?: string;
  security_group_id?: string;
  size?: number;
  state?: string;
  subnet_cidr?: string;
  tags?: DescribeClustersResponseTags[];
  updated?: string;
  vpc_id?: string;
  vswitch_cidr?: string;
  vswitch_id?: string;
  worker_ram_role_name?: string;
  zone_id?: string;
};
export interface CreateAutoscalingConfigBody{
  /**静默时间。扩容出的节点，在静默时间过后，才能进入缩容判断。单位：分钟。 */
  cool_down_duration?: string;
  /**缩容触发时延，节点缩容时需要连续满足触发时延所设定的时间，才能进行缩容。单位：分钟。 */
  unneeded_duration?: string;
  /**缩容阈值，节点上Request的资源与总资源量的比值。 */
  utilization_threshold?: string;
  /**GPU缩容阈值，节点上Request的资源与总资源量的比值。 */
  gpu_utilization_threshold?: string;
  /**弹性灵敏度，判断伸缩的间隔时间。单位：s。 */
  scan_interval?: string;
  /**是否允许进行节点缩容。取值： - `true`：允许缩容。
- `false`：禁止缩容。 */
  scale_down_enabled?: boolean;
  /**节点池扩容顺序策略。取值： - `least-waste` ：默认策略。如果可扩容节点池有多个，从中选择一个资源浪费最少的节点池进行扩容。
- `random`：随机策略。如果可扩容节点池有多个，从中任意选择一个节点池进行扩容。
- `priority `：优先级策略。如果可扩容节点池有多个，会按照您自定义的伸缩组顺序，选择优先级高的节点池进行扩容。
优先级的配置存储在kube-system命名空间下ConfigMap的`cluster-autoscaler-priority-expander`中。当自动伸缩进行扩容时，实时读取此配置，将可扩容的伸缩组ID与配置中伸缩组ID进行匹配，在匹配上的伸缩组中选取优先级最大值的伸缩组作为扩容对象。 */
  expander?: string;
  /**cluster autoscaler是否缩容有kube-system命名空间下的Pods（除了DaemonSet或mirror Pods）的节点。 - `true`：不会缩容。
- `false`：缩容。 */
  skip_nodes_with_system_pods?: boolean;
  /**cluster autoscaler是否缩容有local storage（如EmptyDir 或 HostPath）的pods的节点 - `true`：不会缩容。
- `false`：缩容。 */
  skip_nodes_with_local_storage?: boolean;
  /**缩容时是否驱逐节点上的DaemonSet pods - `true`：驱逐。
- `false`：不会驱逐。 */
  daemonset_eviction_for_nodes?: boolean;
  /**cluster autoscaler缩容节点时等待节点上pod终止的最长时间。单位：s。 */
  max_graceful_termination_sec?: number;
  /**考虑缩容时Pod应该有的最小副本数。 */
  min_replica_count?: number;
  /**极速模式节点缩容成功后，是否删除其对应的K8s Node对象。 */
  recycle_node_deletion_enabled?: boolean;
  /**集群Ready节点数为0时，CA是否扩容。 */
  scale_up_from_zero?: boolean;
};
export interface DescribeClusterV2UserKubeconfigResponse{
  config?: string;
};
export interface DeleteClusterNodesBody{
  /**是否自动排空节点上的Pod。取值： - `true`：自动排空节点上的Pod。
- `false`：不自动排空节点上的Pod。

默认值：`false`。 */
  drain_node?: boolean;
  /**是否同时移除ECS。取值： 
- `true`：同时移除ECS。
- `false`：不同移除ECS。

默认值：`false`。

> 当节点为包年包月实例时，不支持同时移除ECS。 */
  release_node?: boolean;
  /**移除节点列表，节点名称必须是节点在集群中的名称，例如：`cn-hangzhou.192.168.0.70`。 */
  nodes: string[];
};
export interface DeleteClusterNodesResponse{
  /**集群ID。 */
  cluster_id?: string;
  /**请求ID。 */
  request_id?: string;
  /**任务ID。 */
  task_id?: string;
};
export interface RemoveClusterNodesBody{
  /**是否排空节点上的Pod。 */
  drain_node?: boolean;
  /**要移除的Node列表。 */
  nodes: string[];
  /**是否同时释放ECS。 */
  release_node?: boolean;
};
export interface AttachInstancesBody{
  /**待添加实例列表。 */
  instances: string[];
  /**待添加实例的密钥对名称，和`login_password`二选一。 
> 指定`nodepool_id`后，该参数不支持。 */
  key_pair?: string;
  /**待添加实例的SSH登录密码，和`key_pair`二选一。密码规则为8~30个字符，且至少同时包含三项（大小写字母、数字和特殊符号），不支持反斜线（\）和半角双引号（"）两个符号。 
出于安全性考虑，密码传输过程中都是经过加密处理的。 */
  password?: string;
  /**是否将容器数据和镜像存储在数据盘中。取值： 
- `true`：将容器数据和镜像存储在数据盘。

- `false`：不将容器数据和镜像存储在数据盘。

默认值：`false`。


数据盘挂载规则：

- 如果ECS已挂载数据盘，且最后一块数据盘的文件系统未初始化，系统会自动将该数据盘格式化为EXT4，用来存放内容/var/lib/docker、/var/lib/kubelet。
- 如果ECS未挂载数据盘，则不会挂载新的数据盘。
>当选择将数据存储至数据盘并且ECS已挂载数据盘，数据盘内原有数据将丢失，请注意备份数据。 */
  format_disk?: boolean;
  /**是否保留原实例名称。取值： 
- `true`：保留实例名称。

- `false`：不保留实例名称。

默认值：`true`。 */
  keep_instance_name?: boolean;
  /**是否为边缘节点，即ENS节点。取值： 
- `true`：表示添加的节点是边缘节点。

- `false`：表示添加的节点是不是边缘节点。

默认值：`false`。

> 如果是边缘节点，取值必须是`true`，用于标识该节点类型为ENS节点。 */
  is_edge_worker?: boolean;
  /**节点池ID。如果不指定，则将节点添加到默认节点池中。 */
  nodepool_id?: string;
  /**自定义镜像ID，如果不传则使用默认系统镜像。 
>- 实例系统盘镜像将被替换为该镜像。
- 指定`nodepool_id`后，该参数不支持。
 */
  image_id?: string;
  /**节点CPU管理策略。当集群版本在1.12.6及以上时支持以下两种策略： 
- `static`：允许为节点上具有某些资源特征Pod增强其CPU亲和性和独占性。
- `none`：表示启用现有的默认CPU亲和性方案。

默认值：`none`。

> 指定`nodepool_id`后，该参数不支持。 */
  cpu_policy?: string;
  /**节点自定义数据。更多详情，请参见[生成实例自定义数据](~~49121~~)。 
> 指定`nodepool_id`后，该参数不支持。 */
  user_data?: string;
  /**RDS实例列表。  */
  rds_instances?: string[];
  /**容器运行时。 > 指定`nodepool_id`后，该参数不支持。 */
  runtime?: Runtime;
  /**节点标签。标签定义规则： 
- 标签由区分大小写的键值对组成，您最多可以设置20个标签。
- 标签键不可以重复，最长为64个字符；标签值可以为空，最长为128个字符。标签键和标签值都不能以`aliyun`、`acs:`、`https://`或`http://`开头。详情请参见[Labels and Selectors](https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#syntax-and-character-set)。
> 指定`nodepool_id`后，该参数不支持。 */
  tags?: Tag[];
};
export interface AttachInstancesResponseList{
  /**节点添加结果状态码。 */
  code?: string;
  /**实例ID。 */
  instanceId?: string;
  /**节点添加结果描述信息。 */
  message?: string;
};
export interface AttachInstancesResponse{
  /**节点添加信息列表。 */
  list?: AttachInstancesResponseList[];
  /**任务ID。 */
  task_id?: string;
};
export interface DescribeClusterAttachScriptsBody{
  /**节点池ID，添加节点时可以将该节点添加到指定的节点池中。 
> 如果不指定节点池ID，默认将节点添加至默认节点池。 */
  nodepool_id?: string;
  /**手动添加已有实例到集群时，是否对该实例进行数据盘挂载，将容器和镜像存储在数据盘上。取值： 
- `true`：对实例进行数据盘挂载，数据盘内原有数据将丢失，请注意备份数据。
- `false`：不对实例进行数据盘挂载。

默认值：`false`。

数据挂载规则：

- 如果 ECS 已挂载数据盘，且最后一块数据盘的文件系统未初始化，系统会自动将该数据盘格式化为 ext4，用来存放内容/var/lib/docker、/var/lib/kubelet 。
- 如果 ECS 未挂载数据盘，则不会挂载新的数据盘。

 */
  format_disk?: boolean;
  /**添加已有实到集群时，是否保留实例名称。如果不保留，则实例名称格式为`worker-k8s-for-cs-<clusterid>`。取值： 
- `true`：保留实例名称。
- `false`：不保留实例名称，会用系统规则进行替换。

默认值：`true`。 */
  keep_instance_name?: boolean;
  /**如果指定了RDS实例列表，集群节点ECS会自动加入RDS访问白名单。 */
  rds_instances?: string[];
  /**节点CPU架构。支持的CPU架构包括`amd64`、`arm`、`arm64`。 
默认值：`amd64`。

> 当集群类型为边缘托管版时必填。 */
  arch?: string;
  /**节点的接入配置参数。 
> 当集群类型为边缘托管版时必填。 */
  options?: string;
};
export interface DescribeClusterNodesResponseNodes{
  /**节点创建时间。 */
  creation_time?: string;
  /**节点创建时错误信息。 */
  error_message?: string;
  /**节点到期时间。 */
  expired_time?: string;
  /**节点主机名。 */
  host_name?: string;
  /**节点使用的系统镜像ID。 */
  image_id?: string;
  /**节点付费类型。取值： 
- `PrePaid`：预付费，包年包月。选择该类付费方式时，您必须确认自己的账号支持余额支付或信用支付，否则将返回`InvalidPayMethod`的错误提示。

- `PostPaid`：按量付费。 */
  instance_charge_type?: string;
  /**节点实例ID。 */
  instance_id?: string;
  /**节点在集群中的名称。 */
  instance_name?: string;
  /**节点角色类型： 
- Master：集群Master节点。
- Worker：集群Worker节点。 */
  instance_role?: string;
  /**节点运行状态。 */
  instance_status?: string;
  /**节点类型。 */
  instance_type?: string;
  /**节点所属ECS族名称。 */
  instance_type_family?: string;
  /**节点IP地址。 */
  ip_address?: string[];
  /**是否为阿里云实例。取值： 
- `true`：是阿里云实例。

- `false`：不是阿里云实例。 */
  is_aliyun_node?: boolean;
  /**节点名称，集群内节点标识。 */
  node_name?: string;
  /**节点是否就绪。取值： 
- `Ready`：节点已就绪。

- `NotReady`：节点未就绪。

- `Unknown`：未知。

- `Offline`：离线中。 */
  node_status?: string;
  /**节点池ID。 */
  nodepool_id?: string;
  /**节点通过何种方式初始化，例如：手动创建或ROS创建。 */
  source?: string;
  /**节点运行状态。取值： 
- `pending`：创建中。

- `running`：运行中。

- `starting`：启动中。

- `stopping`：停止中。

- `stopped`：已停止。 */
  state?: string;
  /**抢占式实例类型，取值： - NoSpot：非抢占式实例。
- SpotWithPriceLimit：设置抢占实例价格上限。
- SpotAsPriceGo：系统自动出价，跟随当前市场实际价格。
 */
  spot_strategy?: string;
};
export interface DescribeClusterNodesResponsePage{
  /**页码。 */
  page_number?: number;
  /**每页显示的记录数。 */
  page_size?: number;
  /**结果总数。 */
  total_count?: number;
};
export interface DescribeClusterNodesResponse{
  /**节点详情列表。 */
  nodes?: DescribeClusterNodesResponseNodes[];
  /**分页信息。 */
  page?: DescribeClusterNodesResponsePage;
};
export interface CreateClusterNodePoolBodyAutoScaling{
  /**是否启用自动伸缩。取值： - `true`：开启节点池自动伸缩功能。
- `false`：不开启自动伸缩，当取值为false时，其他`auto_scaling`配置参数将不生效。

默认值：`false`。 */
  enable: boolean;
  /**自动伸缩组最大实例数。 */
  max_instances: number;
  /**自动伸缩组最小实例数。 */
  min_instances: number;
  /**自动伸缩类型，按照自动伸缩实例类型划分。取值： 
- `cpu`：普通实例型。

- `gpu`：GPU实例型。

- `gpushare`：GPU共享型。

- `spot`：抢占式实例型。

默认值：`cpu`。 */
  type?: string;
  /**【该字段已废弃】 
是否绑定EIP。取值：

- `true`：绑定EIP。

- `false`：不绑定EIP。

默认值：`false`。 */
  is_bond_eip?: boolean;
  /**【该字段已废弃】 
EIP计费类型。取值：
- `PayByBandwidth`：按固定带宽计费。
- `PayByTraffic`：按使用流量计费。

默认值：`PayByBandwidth`。 */
  eip_internet_charge_type?: string;
  /**【该字段已废弃】 
EIP带宽峰值。单位：Mbps。 */
  eip_bandwidth?: number;
};
export interface CreateClusterNodePoolBodyKubernetesConfig{
  /**是否在ECS节点上安装云监控，安装后可以在云监控控制台查看所创建ECS实例的监控信息，推荐开启。取值： 
- `true`：在ECS节点上安装云监控。

- `false`：不在ECS节点上安装云监控。

默认值：`false`。 */
  cms_enabled?: boolean;
  /**节点CPU管理策略。当集群版本在1.12.6及以上时支持以下两种策略： 
- `static`：允许为节点上具有某些资源特征Pod增强其CPU亲和性和独占性。
- `none`：表示启用现有的默认CPU亲和性方案。

默认值：`none`。 */
  cpu_policy?: string;
  /**节点标签，为Kubernetes集群节点添加标签。 */
  labels?: Tag[];
  /**容器运行时。 */
  runtime: string;
  /**容器运行时版本。  */
  runtime_version: string;
  /**污点配置。 */
  taints?: Taint[];
  /**节点自定义数据。 */
  user_data?: string;
  /**节点名称由三部分组成：前缀 \+ 节点 IP \+ 后缀： 
- 前缀和后缀均可由“.”分隔的一个或多个部分构成，每个部分可以使用小写字母、数字和“-”，节点名称首尾必须为小写字母和数字；
- 节点 IP为完整的节点私网 IP 地址；

传参包含四个部分，由逗号分隔，例如：参数传入"customized,aliyun,ip,com"字符串（其中“customized”和"ip"为固定的字符串，aliyun为前缀，com为后缀），则节点的名称为：aliyun.192.168.xxx.xxx.com。 */
  node_name_mode?: string;
};
export interface CreateClusterNodePoolBodyNodepoolInfo{
  /**节点池名称。 */
  name: string;
  /**节点池所在资源ID。 */
  resource_group_id?: string;
  /**节点池类型，取值范围： 
- `ess`：节点池。
- `edge`：边缘节点池。
 */
  type?: string;
};
export interface CreateClusterNodePoolBodyScalingGroupSpotPriceLimit{
  /**抢占式实例规格。 */
  instance_type?: string;
  /**单台实例上限价格。 
<props="china">单位：元/小时。

</props>

<props="intl">单位：美元/小时。</props>
 */
  price_limit?: string;
};
export interface CreateClusterNodePoolBodyScalingGroupTags{
  /**标签的名称。 */
  key?: string;
  /**标签值。 */
  value?: string;
};
export interface CreateClusterNodePoolBodyScalingGroupPrivatePoolOptions{
  /**私有节点池ID。 */
  id?: string;
  /**私有节点池类型，实例启动的私有池容量选项。弹性保障服务或容量预定服务在生效后会生成私有池容量，供实例启动时选择。取值： - `Open`：开放模式。将自动匹配开放类型的私有池容量。如果没有符合条件的私有池容量，则使用公共池资源启动。
- `Target`：指定模式。使用指定的私有池容量启动实例，如果该私有池容量不可用，则实例会启动失败。
- `None`：不使用模式。实例启动将不使用私有池容量。 */
  match_criteria?: string;
};
export interface CreateClusterNodePoolBodyScalingGroup{
  /**节点池节点是否开启自动续费，当`instance_charge_type`取值为`PrePaid`时才生效。取值： 
- `true`：自动续费。
- `false`：不自动续费。

默认值：`true`。 */
  auto_renew?: boolean;
  /**节点池节点自动续费周期，当选择预付费和自动续费时才生效，且为必选值。当`PeriodUnit=Month`时，取值范围：{1, 2, 3, 6, 12}。 
默认值：1
 */
  auto_renew_period?: number;
  /**节点池节点数据盘配置。 */
  data_disks?: DataDisk[];
  /**自定义镜像ID，默认使用系统提供的镜像。 */
  image_id?: string;
  /**节点池节点付费类型，取值：  
- `PrePaid`：预付费。

- `PostPaid`：按量付费。

默认值：`PostPaid`。 */
  instance_charge_type: string;
  /**节点池节点实例规格。 */
  instance_types: string[];
  /**免密登录密钥对名称，和`login_password`二选一。 
>如果创建托管节点池，则只支持`key_pair`。 */
  key_pair?: string;
  /**SSH登录密码，和`key_pair `二选一。密码规则为8~30个字符，且至少同时包含三项（大小写字母、数字和特殊符号）。 */
  login_password?: string;
  /**节点池节点包年包月时长，当`instance_charge_type`取值为`PrePaid`时才生效且为必选值，取值范围：`period_unit`取值为Month时，`period`取值范围：{ 1， 2， 3，6，12}。 
默认值：1。 */
  period?: number;
  /**节点池节点付费周期，当指定为`PrePaid`的时候需要指定周期。Month：以月为计时单位。 */
  period_unit?: string;
  /**操作系统发行版。取值： - `CentOS`
- `AliyunLinux`
- `Windows`
- `WindowsCore`

默认值：`AliyunLinux`。 */
  platform?: string;
  /**RDS实例列表。 */
  rds_instances?: string[];
  /**抢占式实例类型。取值： 
- `NoSpot`：非抢占式实例。

- `SpotWithPriceLimit`：设置抢占实例价格上限。

- `SpotAsPriceGo`：系统自动出价，跟随当前市场实际价格。

更多信息，请参见[抢占式实例](~~165053~~)。 */
  spot_strategy?: string;
  /**当前单台抢占式实例规格市场价格区间配置。 */
  spot_price_limit?: CreateClusterNodePoolBodyScalingGroupSpotPriceLimit[];
  /**伸缩组模式，取值： 
- `release`：标准模式，根据申请资源值的使用量，通过创建、释放ECS的方式进行伸缩。
- `recycle`：极速模式，通过创建、停机、启动的方式进行伸缩，提高再次伸缩的速度（停机时计算资源不收费，只收取存储费用，本地盘机型除外）。

默认值：`release`。 */
  scaling_policy?: string;
  /**节点池安全组ID，与`security_group_ids`二选一，推荐使用`security_group_ids`。 */
  security_group_id?: string;
  /**安全组ID列表，与`security_group_id`二选一，推荐使用`security_group_ids`，当同时指定`security_group_id`和`security_group_ids`将优先使用`security_group_ids`。 */
  security_group_ids?: string[];
  /**节点系统盘类型，取值： 
- `cloud_efficiency`：高效云盘。
- `cloud_ssd`：SSD云盘。
- `cloud_essd`：ESSD云盘。

默认值：`cloud_efficiency` */
  system_disk_category: string;
  /**节点系统盘大小，单位：GiB。 
取值范围：\[40,500\]。 */
  system_disk_size: number;
  /**节点系统盘磁盘性能，只对ESSD磁盘生效。 - PL0：并发极限I/O性能中等，读写时延较为稳定。
- PL1：并发极限I/O性能中等，读写时延较为稳定。
- PL2：并发极限I/O性能较高，读写时延稳定。
- PL3：并发极限I/O性能极高，读写时延极稳定。 */
  system_disk_performance_level?: string;
  /**仅为ECS实例添加标签。 
标签键不可以重复，最大长度为128个字符；标签键和标签值都不能以“aliyun”、“acs:”开头，或包含“https://”、“http://”。 */
  tags?: CreateClusterNodePoolBodyScalingGroupTags[];
  /**虚拟交换机ID，取值范围：\[1,20\]。 
> 为保证高可用性，建议选择不同可用区的虚拟交换机。 */
  vswitch_ids: string[];
  /**多可用区伸缩组ECS实例扩缩容策略。取值： 
- `PRIORITY`：根据您定义的虚拟交换机（VSwitchIds.N）扩缩容。当优先级较高的虚拟交换机所在可用区无法创建ECS实例时，自动使用下一优先级的虚拟交换机创建ECS实例。

- `COST_OPTIMIZED`：按vCPU单价从低到高进行尝试创建。当伸缩配置设置了抢占式计费方式的多实例规格时，优先创建对应抢占式实例。您可以继续通过`CompensateWithOnDemand`参数指定当抢占式实例由于库存等原因无法创建时，是否自动尝试以按量付费的方式创建。

  >`COST_OPTIMIZED`仅在伸缩配置设置了多实例规格或者选用了抢占式实例的情况下生效。

- `BALANCE`：在伸缩组指定的多可用区之间均匀分配ECS实例。如果由于库存不足等原因可用区之间变得不平衡，您可以通过API [RebalanceInstances](~~71516~~)平衡资源。

默认值：`PRIORITY`。 */
  multi_az_policy?: string;
  /**伸缩组所需要按量实例个数的最小值，取值范围：\[0,1000\]。当按量实例个数少于该值时，将优先创建按量实例。 */
  on_demand_base_capacity?: number;
  /**伸缩组满足最小按量实例数（`on_demand_base_capacity`）要求后，超出的实例中按量实例应占的比例。取值范围：\[0,100\]。 */
  on_demand_percentage_above_base_capacity?: number;
  /**指定可用实例规格的个数，伸缩组将按成本最低的多个规格均衡创建抢占式实例。取值范围：\[1,10\]。 */
  spot_instance_pools?: number;
  /**是否开启补齐抢占式实例。开启后，当收到抢占式实例将被回收的系统消息时，伸缩组将尝试创建新的实例，替换掉将被回收的抢占式实例。取值： 
- `true`：开启补齐抢占式实例。
- `false`：不开启补齐抢占式实例。
 */
  spot_instance_remedy?: boolean;
  /**当`multi_az_policy`取值为`COST_OPTIMIZED`时，如果因价格、库存等原因无法创建足够的抢占式实例，是否允许自动尝试创建按量实例满足ECS实例数量要求。取值： 
- `true`：允许自动尝试创建按量实例满足ECS实例数量要求。
- `false`：不允许自动尝试创建按量实例满足ECS实例数量要求。 */
  compensate_with_on_demand?: boolean;
  /**公网IP收费类型。取值： 
- PayByBandwidth：按固定带宽计费。
- PayByTraffic：按使用流量计费。 */
  internet_charge_type?: string;
  /**节点公网IP出带宽最大值，单位为Mbps（Mega bit per second），取值范围：\[1,100\] */
  internet_max_bandwidth_out?: number;
  /**操作系统镜像类型，和`platform`参数二选一，取值范围： 
- `AliyunLinux`：Alinux2镜像。
- `AliyunLinux3`：Alinux3镜像。
- `AliyunLinux3Arm64`：Alinux3镜像ARM版。
- `AliyunLinuxUEFI`：Alinux2镜像UEFI版。
- `CentOS`：CentOS镜像。
- `Windows`：Windows镜像。
- `WindowsCore`：WindowsCore镜像。
- `ContainerOS`：容器优化镜像。 */
  image_type?: string;
  /**部署集ID。 */
  deploymentset_id?: string;
  /**节点池期望节点数量。 */
  desired_size?: number;
  /**私有节点池配置。 */
  private_pool_options?: CreateClusterNodePoolBodyScalingGroupPrivatePoolOptions;
};
export interface CreateClusterNodePoolBodyTeeConfig{
  /**是否开启加密计算集群。 */
  tee_enable: boolean;
};
export interface CreateClusterNodePoolBodyManagementUpgradeConfig{
  /**是否启用自动升级，取值： 
- `true`：启用自动升级。

- `false`：不启用自动升级。 */
  auto_upgrade?: boolean;
  /**额外节点数量。 */
  surge?: number;
  /**额外节点比例， 和`surge`二选一。 */
  surge_percentage?: number;
  /**最大不可用节点数量。 取值范围：\[1,1000\]

默认值：1。 */
  max_unavailable: number;
};
export interface CreateClusterNodePoolBodyManagement{
  /**是否开启托管版节点池，取值： 
- `true`：开启托管节点池。

- `false`：不开启托管节点池，只有当enable=true时，其他相关配置才生效。 */
  enable: boolean;
  /**自动修复，仅当`enable=true`时生效。 
- `true`：自动修复。

- `false`：不自动修复。 */
  auto_repair?: boolean;
  /**自动升级配置，仅当`enable=true`时生效。 */
  upgrade_config?: CreateClusterNodePoolBodyManagementUpgradeConfig;
};
export interface CreateClusterNodePoolBodyInterconnectConfig{
  /**【该字段已废弃】 
边缘增强型节点池绑定的云企业网实例ID(CENID)。 */
  cen_id?: string;
  /**【该字段已废弃】 
边缘增强型节点池绑定的云连接网实例ID(CCNID)。 */
  ccn_id?: string;
  /**【该字段已废弃】 
边缘增强型节点池绑定的云连接网实例所属的地域。 */
  ccn_region_id?: string;
  /**【该字段已废弃】 
边缘增强型节点池的网络带宽，单位：Mbps。 */
  bandwidth?: number;
  /**【该字段已废弃】 
边缘增强型节点池的购买时长，单位：月。 */
  improved_period?: string;
};
export interface CreateClusterNodePoolBody{
  /**自动伸缩配置。 */
  auto_scaling?: CreateClusterNodePoolBodyAutoScaling;
  /**集群相关配置。 */
  kubernetes_config?: CreateClusterNodePoolBodyKubernetesConfig;
  /**节点池配置。 */
  nodepool_info?: CreateClusterNodePoolBodyNodepoolInfo;
  /**节点池扩容组配置。 */
  scaling_group?: CreateClusterNodePoolBodyScalingGroup;
  /**加密计算集群配置。 */
  tee_config?: CreateClusterNodePoolBodyTeeConfig;
  /**托管节点池配置。 */
  management?: CreateClusterNodePoolBodyManagement;
  /**【该字段已废弃，请使用desired_size代替】 
节点池节点数量。 */
  count?: number;
  /**边缘节点池的网络类型，该值只对`type`为`edge`类型的节点池有意义，取值范围： 
- `basic`：基础型。
- `improved`：增强型。
- `private`: 专用型。1.22及以上版本支持。 */
  interconnect_mode?: string;
  /**【该字段已废弃】 
边缘节点池配置。 */
  interconnect_config?: CreateClusterNodePoolBodyInterconnectConfig;
  /**边缘节点池允许容纳的最大节点数量.。该参数大于等于0。0表示无额外限制（仅受限于集群整体可以容纳的节点数，节点池本身无额外限制）。边缘节点池该参数值往往大于0；ess类型节点池和默认的edge类型节点池该参数值为0。 */
  max_nodes?: number;
};
export interface CreateClusterNodePoolResponse{
  /**节点池ID。 */
  nodepool_id?: string;
};
export interface DeleteClusterNodepoolResponse{
  /**请求ID。 */
  request_id?: string;
};
export interface ScaleClusterNodePoolBody{
  /**扩容节点数量。受当前集群节点配额限制，单次操作最多扩容500个节点。 */
  count?: number;
};
export interface ScaleClusterNodePoolResponse{
  /**任务ID。 */
  task_id?: string;
};
export interface ModifyClusterNodePoolBodyAutoScaling{
  /**EIP带宽峰值。 */
  eip_bandwidth?: number;
  /**EIP计费类型，取值： 
- `PayByBandwidth`：按固定带宽计费。
- `PayByTraffic`：按使用流量计费。

默认值：`PayByBandwidth`。 */
  eip_internet_charge_type?: string;
  /**是否启用自动伸缩，取值： 
- `true`：开启节点池自动伸缩功能。
- `false`：不开启自动伸缩，当取值为false时，`auto_scaling`内的其他配置参数将不生效。

默认值：`false`。 */
  enable?: boolean;
  /**是否绑定EIP，取值： 
- `true`：绑定EIP。
- `false`：不绑定EIP。

默认值：`false`。 */
  is_bond_eip?: boolean;
  /**最大实例数。 */
  max_instances?: number;
  /**最小实例数。 */
  min_instances?: number;
  /**自动伸缩类型，按照自动伸缩实例类型划分。取值： 
- `cpu`：普通实例型。
- `gpu`：GPU实例型。
- `gpushare`：GPU共享型。
- `spot`：抢占式实例型。

默认值：`cpu`。 */
  type?: string;
};
export interface ModifyClusterNodePoolBodyKubernetesConfig{
  /**是否在ECS节点上安装云监控，安装后可以在云监控控制台查看所创建ECS实例的监控信息，推荐开启。取值： 
- `true`：在ECS节点上安装云监控。
- `false`：不在ECS节点上安装云监控。

默认值：`false`。 */
  cms_enabled?: boolean;
  /**节点CPU管理策略。当集群版本在1.12.6及以上时支持以下两种策略： 
- `static`：允许为节点上具有某些资源特征Pod增强其CPU亲和性和独占性。
- `none`：表示启用现有的默认CPU亲和性方案。

默认值：`none`。 */
  cpu_policy?: string;
  /**节点标签，为Kubernetes集群节点添加标签。标签定义规则： 
- 标签由区分大小写的键值对组成，您最多可以设置20个标签。
- 标签键不可以重复，最长为64个字符；标签值可以为空，最长为128个字符。标签键和标签值都不能以`aliyun`、`acs:`、`https://`或`http://`开头。更多信息，请参见[Labels and Selectors](https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#syntax-and-character-set)。 */
  labels?: Tag[];
  /**容器运行时名称。 */
  runtime?: string;
  /**容器运行时版本。 */
  runtime_version?: string;
  /**节点污点配置。 */
  taints?: Taint[];
  /**节点池自定义数据。更多信息，请参见[生成实例自定义数据](~~49121~~)。 */
  user_data?: string;
};
export interface ModifyClusterNodePoolBodyNodepoolInfo{
  /**节点池名称。 
命名规则：由数字、汉字、英文字符或短划线（-）组成，长度范围1~63个字符，且不能以短划线（-）开头。 */
  name?: string;
  /**资源组ID。 */
  resource_group_id?: string;
};
export interface ModifyClusterNodePoolBodyScalingGroupSpotPriceLimit{
  /**抢占式实例规格。 */
  instance_type?: string;
  /**单台实例上限价格。 
<props="china">单位：元/小时。

</props>

<props="intl">单位：美元/小时。</props>
 */
  price_limit?: string;
};
export interface ModifyClusterNodePoolBodyScalingGroupPrivatePoolOptions{
  /**私有节点池ID。 */
  id?: string;
  /**私有节点池类型，实例启动的私有池容量选项。弹性保障服务或容量预定服务在生效后会生成私有池容量，供实例启动时选择。取值： - `Open`：开放模式。将自动匹配开放类型的私有池容量。如果没有符合条件的私有池容量，则使用公共池资源启动。
- `Target`：指定模式。使用指定的私有池容量启动实例，如果该私有池容量不可用，则实例会启动失败。
- `None`：不使用模式。实例启动将不使用私有池容量。 */
  match_criteria?: string;
};
export interface ModifyClusterNodePoolBodyScalingGroup{
  /**节点数据盘配置，取值范围\[0,10\]。最多支持添加10块数据盘。 */
  data_disks?: DataDisk[];
  /**节点池节点付费类型，取值： 
- `PrePaid`：预付费
- `PostPaid`：按量付费

默认值：`PostPaid`。 */
  instance_charge_type?: string;
  /**节点包年包月时长，当`instance_charge_type`取值为`PrePaid`时才生效且为必选值。 
当`PeriodUnit=Month`时，取值范围：{1, 2 , 3, 6, 12, 24, 36, 48, 60}。 */
  period?: number;
  /**节点付费周期，当`instance_charge_type`取值为`PrePaid`时时候需要指定周期。 
目前只支持以月为计时单位。

默认值：`Month`。 */
  period_unit?: string;
  /**节点是否开启自动续费，当`instance_charge_type`取值为`PrePaid`时才生效。取值： 
- `true`：自动续费。
- `false`：不自动续费。

默认值：`true`。 */
  auto_renew?: boolean;
  /**节点自动续费周期。当`instance_charge_type`取值为`PrePaid`时才生效，且为必选值。 
当`PeriodUnit=Month`时，取值范围：{1, 2 , 3, 6, 12}。 */
  auto_renew_period?: number;
  /**操作系统平台。取值： 
- `AliyunLinux`
- `CentOS`
- `Windows`
- `WindowsCore` */
  platform?: string;
  /**自定义镜像ID。可通过`DescribeKubernetesVersionMetadata`查询系统支持的镜像，默认使用系统最新镜像。 */
  image_id?: string;
  /**抢占式实例类型，取值： 
- `NoSpot`：非抢占式实例。
- `SpotWithPriceLimit`：设置抢占实例价格上限。
- `SpotAsPriceGo`：系统自动出价，跟随当前市场实际价格。

更多信息，请参见[抢占式实例](~~157759~~)。 */
  spot_strategy?: string;
  /**抢占实例市场价格区间配置。 */
  spot_price_limit?: ModifyClusterNodePoolBodyScalingGroupSpotPriceLimit[];
  /**节点实例规格列表，您可以选择多个实例规格作为备选，每个节点创建时，将从第一个规格开始尝试购买，直到创建成功。最终购买的实例规格可能随库存变化而不同。 */
  instance_types?: string[];
  /**密钥对名称，和`login_password`二选一。当节点池为托管版节点池时，只支持`key_pair`。 */
  key_pair?: string;
  /**SSH登录密码，和`key_pair`二选一。密码规则为8~30个字符，且至少同时包含三项（大小写字母、数字和特殊符号）。 */
  login_password?: string;
  /**RDS实例列表。 */
  rds_instances?: string[];
  /**伸缩组模式，取值： 
- `release`：标准模式，根据申请资源值的使用量，通过创建、释放ECS的方式进行伸缩。
- `recycle`：极速模式，通过创建、停机、启动的方式进行伸缩，提高再次伸缩的速度（停机时计算资源不收费，只收取存储费用，本地盘机型除外）。 */
  scaling_policy?: string;
  /**节点系统盘类型，取值： 
- `cloud_efficiency`：高效云盘。
- `cloud_ssd`：SSD云盘。

默认值：`cloud_ssd`。 */
  system_disk_category?: string;
  /**节点系统盘大小，单位为GiB。 
取值范围：\[20,500\]。

该参数的取值必须大于或者等于max{20, ImageSize}。

默认值：max{40, 参数ImageId对应的镜像大小}。 */
  system_disk_size?: number;
  /**节点系统盘磁盘性能等级， 仅对ESSD磁盘生效。磁盘性能等级和磁盘大小有关。更多信息，请参见[ESSD云盘](~~122389~~) */
  system_disk_performance_level?: string;
  /**仅为ECS实例添加标签。 
标签键不可以重复，最大长度为128个字符；标签键和标签值都不能以“aliyun”、“acs:”开头，或包含“https://”、“http://”。 */
  tags?: Tag[];
  /**虚拟交换机ID列表，取值范围\[1,20\]。 
> 为保证高可用性，建议选择不同可用区的虚拟交换机。 */
  vswitch_ids?: string[];
  /**多可用区伸缩组ECS实例扩缩容策略。取值： 
- `PRIORITY`：根据您定义的虚拟交换机（VSwitchIds.N）扩缩容。当优先级较高的虚拟交换机所在可用区无法创建ECS实例时，自动使用下一优先级的虚拟交换机创建ECS实例。

- `COST_OPTIMIZED`：按vCPU单价从低到高进行尝试创建。当伸缩配置设置了抢占式计费方式的多实例规格时，优先创建对应抢占式实例。您可以继续通过`CompensateWithOnDemand`参数指定当抢占式实例由于库存等原因无法创建时，是否自动尝试以按量付费的方式创建。

  > `COST_OPTIMIZED`仅在伸缩配置设置了多实例规格或者选用了抢占式实例的情况下生效。

- `BALANCE`：在伸缩组指定的多可用区之间均匀分配ECS实例。如果由于库存不足等原因可用区之间变得不平衡，您可以通过API `RebalanceInstances`平衡资源。更多信息，请参见[RebalanceInstances](~~71516~~)。

默认值：`PRIORITY`。 */
  multi_az_policy?: string;
  /**伸缩组所需要按量实例个数的最小值，取值范围：\[0,1000\]。当按量实例个数少于该值时，将优先创建按量实例。 */
  on_demand_base_capacity?: number;
  /**伸缩组满足最小按量实例数（`on_demand_base_capacity`）要求后，超出的实例中按量实例应占的比例。取值范围：\[0,100\]。 */
  on_demand_percentage_above_base_capacity?: number;
  /**指定可用实例规格的个数，伸缩组将按成本最低的多个规格均衡创建抢占式实例。取值范围：\[1,10\]。 */
  spot_instance_pools?: number;
  /**是否开启补齐抢占式实例。开启后，当收到抢占式实例将被回收的系统消息时，伸缩组将尝试创建新的实例，替换掉将被回收的抢占式实例。取值： 
- `true`：开启补齐抢占式实例。
- `false`：不开启补齐抢占式实例。
 */
  spot_instance_remedy?: boolean;
  /**当`multi_az_policy`取值为`COST_OPTIMIZED`时，如果因价格、库存等原因无法创建足够的抢占式实例，是否允许自动尝试创建按量实例满足ECS实例数量要求。取值： 
- `true`：允许自动尝试创建按量实例满足ECS实例数量要求。
- `false`：不允许自动尝试创建按量实例满足ECS实例数量要求。 */
  compensate_with_on_demand?: boolean;
  /**公网IP收费类型。取值： 
- `PayByBandwidth`：按固定带宽计费。
- `PayByTraffic`：按使用流量计费。 */
  internet_charge_type?: string;
  /**节点公网IP出带宽最大值，单位为Mbps（Mega bit per second），取值范围：[1,100]。 */
  internet_max_bandwidth_out?: number;
  /**节点池期望节点数。 */
  desired_size?: number;
  /**私有节点池配置。 */
  private_pool_options?: ModifyClusterNodePoolBodyScalingGroupPrivatePoolOptions;
};
export interface ModifyClusterNodePoolBodyTeeConfig{
  /**是否开启加密计算集群，取值： 
- `true`：开启。
- `false`：不开启。

默认值：`false`。 */
  tee_enable?: boolean;
};
export interface ModifyClusterNodePoolBodyManagementUpgradeConfig{
  /**是否启用自动升级： 
- true：启用。
- false：不启用。

默认值：`true`。 */
  auto_upgrade?: boolean;
  /**额外节点数量。升级的时候节点会不可用，您可以创建额外节点补偿集群的负载。 
> 建议创建的额外节点不超过当前的节点数。 */
  surge?: number;
  /**额外节点比例，和`surge`二选一。 */
  surge_percentage?: number;
  /**最大不可用节点数量。 
取值范围：\[1,1000\]

默认值：1。 */
  max_unavailable?: number;
};
export interface ModifyClusterNodePoolBodyManagement{
  /**是否开启托管节点池，取值： 
- `true`：开启托管节点池。
- `false`：不开启托管节点池，只有当`enable=true`时，其他相关配置才生效。

默认值：`false`。 */
  enable?: boolean;
  /**是否自动修复，仅当`enable=true`时生效。取值： 
- `true`：自动修复。
- `false`：不自动修复。

默认值：`true`。 */
  auto_repair?: boolean;
  /**自动升级配置，仅当`enable=true`时生效。 */
  upgrade_config?: ModifyClusterNodePoolBodyManagementUpgradeConfig;
};
export interface ModifyClusterNodePoolBody{
  /**自动伸缩配置。 */
  auto_scaling?: ModifyClusterNodePoolBodyAutoScaling;
  /**集群相关配置。 */
  kubernetes_config?: ModifyClusterNodePoolBodyKubernetesConfig;
  /**节点池配置。 */
  nodepool_info?: ModifyClusterNodePoolBodyNodepoolInfo;
  /**扩容组配置。 */
  scaling_group?: ModifyClusterNodePoolBodyScalingGroup;
  /**加密计算集群配置。 */
  tee_config?: ModifyClusterNodePoolBodyTeeConfig;
  /**托管节点池配置。 */
  management?: ModifyClusterNodePoolBodyManagement;
  /**同步更新节点标签及污点。 */
  update_nodes?: boolean;
};
export interface ModifyClusterNodePoolResponse{
  /**任务ID。 */
  task_id?: string;
  /**节点池ID。 */
  nodepool_id?: string;
};
export interface DescribeClusterNodePoolsResponseNodepoolsAutoScaling{
  /**EIP带宽峰值。 */
  eip_bandwidth?: number;
  /**是否绑定EIP，取值： 
- `true`：绑定EIP。
- `false`：不绑定EIP。

 */
  is_bond_eip?: boolean;
  /**EIP计费类型，取值： 
- `PayByBandwidth`：按固定带宽计费。
- `PayByTraffic`：按使用流量计费。
 */
  eip_internet_charge_type?: string;
  /**是否启用自动伸缩，取值： 
- `true`：开启节点池自动伸缩功能。
- `false`：不开启自动伸缩，当取值为`false`时，`auto_scaling`内的其他配置参数将不生效。

 */
  enable?: boolean;
  /**最大实例数。 */
  max_instances?: number;
  /**最小实例数。 */
  min_instances?: number;
  /**自动伸缩类型，按照自动伸缩实例类型划分。取值： 
- `cpu`：普通实例型。
- `gpu`：GPU实例型。
- `gpushare`：GPU共享型。
- `spot`：抢占式实例型。
 */
  type?: string;
};
export interface DescribeClusterNodePoolsResponseNodepoolsKubernetesConfig{
  /**是否在ECS节点上安装云监控，安装后可以在云监控控制台查看所创建ECS实例的监控信息，推荐开启。取值： 
- `true`：在ECS节点上安装云监控。
- `false`：不在ECS节点上安装云监控

 */
  cms_enabled?: boolean;
  /**节点CPU管理策略。当集群版本在1.12.6及以上时支持以下两种策略： 
- `static`：允许为节点上具有某些资源特征Pod增强其CPU亲和性和独占性。
- `none`：表示启用现有的默认CPU亲和性方案。 */
  cpu_policy?: string;
  /**节点标签，为Kubernetes集群节点添加标签。标签定义规则： 
- 标签由区分大小写的键值对组成，您最多可以设置20个标签。
- 标签键不可以重复，最长为64个字符；标签值可以为空，最长为128个字符。标签键和标签值都不能以`aliyun`、`acs:`、`https://`或`http://`开头。更多信息，请参见[Labels and Selectors](https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#syntax-and-character-set)。 */
  labels?: Tag[];
  /**容器运行时名称。 */
  runtime?: string;
  /**容器运行时版本。 */
  runtime_version?: string;
  /**节点污点信息。污点和容忍度（Toleration）相互配合，可以用来避免Pod被分配到不合适的节点上。更多信息，请参见[taint-and-toleration](https://kubernetes.io/zh/docs/concepts/scheduling-eviction/taint-and-toleration/)。 */
  taints?: Taint[];
  /**节点池自定义数据。更多详情，请参见[生成实例自定义数据](~~49121~~)。 */
  user_data?: string;
  /**节点名称由三部分组成：前缀 \+ 节点 IP 地址子串 \+ 后缀： 
- 前缀和后缀均可由“.”分隔的一个或多个部分构成，每个部分可以使用小写字母、数字和“-”，节点名称首尾必须为小写字母和数字；
- IP 地址段长度指截取节点 IP 地址末尾的位数，取值范围 5-12

例如：节点 IP 地址为：************，指定前缀为 aliyun.com，IP 地址段长度为 5，后缀为 test，则节点名称为 aliyun.com00055test */
  node_name_mode?: string;
};
export interface DescribeClusterNodePoolsResponseNodepoolsNodepoolInfo{
  /**节点池创建时间。 */
  created?: string;
  /**是否为默认节点池，通常一个集群仅有一个默认节点池。取值： 
- `true`：设置为默认节点池。
- `false`：不设置为默认节点池。

 */
  is_default?: boolean;
  /**节点池名称。 
命名规则：由数字、汉字、英文字符或短划线（-）组成，长度范围1~63个字符，且不能以短划线（-）开头。 */
  name?: string;
  /**节点池ID。 */
  nodepool_id?: string;
  /**地域ID。 */
  region_id?: string;
  /**资源组ID。 */
  resource_group_id?: string;
  /**节点池类型。取值： - `edge`：边缘节点池。
- `ess`：云端节点池。 */
  type?: string;
  /**节点池更新时间。 */
  updated?: string;
};
export interface DescribeClusterNodePoolsResponseNodepoolsScalingGroupSpotPriceLimit{
  /**抢占式实例规格。 */
  instance_type?: string;
  /**单台实例市场价格区间。 
<props="china">单位：元/时。</props>

<props="intl">单位：美元/时。</props>
 */
  price_limit?: string;
};
export interface DescribeClusterNodePoolsResponseNodepoolsScalingGroupPrivatePoolOptions{
  /**私有池ID。即弹性保障服务ID或容量预定服务ID。 */
  id?: string;
  /**私有节点池类型，实例启动的私有池容量选项。弹性保障服务或容量预定服务在生效后会生成私有池容量，供实例启动时选择。取值： 
- `Open`：开放模式。将自动匹配开放类型的私有池容量。如果没有符合条件的私有池容量，则使用公共池资源启动。

- `Target`：指定模式。使用指定的私有池容量启动实例，如果该私有池容量不可用，则实例会启动失败。

- `None`：不使用模式。实例启动将不使用私有池容量。 */
  match_criteria?: string;
};
export interface DescribeClusterNodePoolsResponseNodepoolsScalingGroup{
  /**节点是否开启自动续费，当`instance_charge_type`取值为`PrePaid`时才生效，取值： 
- `true`：自动续费。
- `false`：不自动续费。

 */
  auto_renew?: boolean;
  /**节点自动续费周期。当`instance_charge_type`取值为`PrePaid`时才生效，且为必选值。 
当`PeriodUnit=Month`时，取值范围：{1, 2, 3, 6, 12}。 */
  auto_renew_period?: number;
  /**节点数据盘类型、大小等配置的组合。 */
  data_disks?: DataDisk[];
  /**自定义镜像ID，可通过`DescribeKubernetesVersionMetadata`查询系统支持的镜像。  */
  image_id?: string;
  /**节点池节点付费类型，取值： 
- `PrePaid`：预付费。
- `PostPaid`：按量付费。

 */
  instance_charge_type?: string;
  /**节点实例规格列表，您可以选择多个实例规格作为备选，每个节点创建时，将从第一个规格开始尝试购买，直到创建成功。最终购买的实例规格可能随库存变化而不同。 */
  instance_types?: string[];
  /**多可用区伸缩组ECS实例扩缩容策略。取值： 
- `PRIORITY`：根据您定义的虚拟交换机（VSwitchIds.N）扩缩容。当优先级较高的虚拟交换机所在可用区无法创建ECS实例时，自动使用下一优先级的虚拟交换机创建ECS实例。

- `COST_OPTIMIZED`：按vCPU单价从低到高进行尝试创建。当伸缩配置设置了抢占式计费方式的多实例规格时，优先创建对应抢占式实例。您可以继续通过`CompensateWithOnDemand`参数指定当抢占式实例由于库存等原因无法创建时，是否自动尝试以按量付费的方式创建。

  >`COST_OPTIMIZED`仅在伸缩配置设置了多实例规格或者选用了抢占式实例的情况下生效。

- `BALANCE`：在伸缩组指定的多可用区之间均匀分配ECS实例。如果由于库存不足等原因可用区之间变得不平衡，您可以通过API `RebalanceInstances`平衡资源。更多信息，请参见[RebalanceInstances](~~71516~~)。

 */
  multi_az_policy?: string;
  /**伸缩组所需要按量实例个数的最小值，取值范围：\[0,1000\]。当按量实例个数少于该值时，将优先创建按量实例。 */
  on_demand_base_capacity?: number;
  /**伸缩组满足最小按量实例数（`on_demand_base_capacity`）要求后，超出的实例中按量实例应占的比例。取值范围：\[0,100\]。 */
  on_demand_percentage_above_base_capacity?: number;
  /**指定可用实例规格的个数，伸缩组将按成本最低的多个规格均衡创建抢占式实例。取值范围：\[1,10\]。 */
  spot_instance_pools?: number;
  /**是否开启补齐抢占式实例。开启后，当收到抢占式实例将被回收的系统消息时，伸缩组将尝试创建新的实例，替换掉将被回收的抢占式实例。取值： 
- `true`：开启补齐抢占式实例。
- `false`：不开启补齐抢占式实例。

 */
  spot_instance_remedy?: boolean;
  /**当`multi_az_policy`取值为`COST_OPTIMIZED`时，如果因价格、库存等原因无法创建足够的抢占式实例，是否允许自动尝试创建按量实例满足ECS实例数量要求。取值： 
- `true`：允许自动尝试创建按量实例满足ECS实例数量要求。
- `false`：不允许自动尝试创建按量实例满足ECS实例数量要求。 */
  compensate_with_on_demand?: boolean;
  /**节点包年包月时长，当`instance_charge_type`取值为`PrePaid`时才生效且为必选值。 
当`PeriodUnit=Month`时，取值范围：{1, 2 , 3, 6, 12, 24, 36, 48, 60}。 */
  period?: number;
  /**节点付费周期，当`instance_charge_type`取值为`PrePaid`时才生效。 
`Month`：以月为计时单位，当前只支持以月为单位。

 */
  period_unit?: string;
  /**操作系统发行版。取值： 
- `CentOS`
- `AliyunLinux`
- `Windows`
- `WindowsCore` */
  platform?: string;
  /**Worker RAM角色名称，授权ECS实例为集群Woker节点。 */
  ram_policy?: string;
  /**抢占式实例类型，取值： - NoSpot：非抢占式实例。
- SpotWithPriceLimit：设置抢占实例价格上限。
- SpotAsPriceGo：系统自动出价，跟随当前市场实际价格。

更多信息，请参见[抢占式实例](~~157759~~)。

 */
  spot_strategy?: string;
  /**抢占式实例市场价格区间配置。 */
  spot_price_limit?: DescribeClusterNodePoolsResponseNodepoolsScalingGroupSpotPriceLimit[];
  /**如果指定了RDS实例列表，集群节点ECS会自动加入RDS访问白名单。 */
  rds_instances?: string[];
  /**伸缩组ID。 */
  scaling_group_id?: string;
  /**伸缩组模式，取值： 
- `release`：标准模式，根据申请资源值的使用量，通过创建、释放ECS的方式进行伸缩。
- `recycle`：极速模式，通过创建、停机、启动的方式进行伸缩，提高再次伸缩的速度（停机时计算资源不收费，只收取存储费用，本地盘机型除外）。 */
  scaling_policy?: string;
  /**节点池安全组ID，当节点池绑定多个安全组时，为`security_group_ids`中的第一个值。 */
  security_group_id?: string;
  /**节点池安全组ID列表。 */
  security_group_ids?: string[];
  /**节点系统盘类型，取值： 
- `cloud_efficiency`：高效云盘。
- `cloud_ssd`：SSD云盘。
 */
  system_disk_category?: string;
  /**节点系统盘大小，单位为GiB。 
取值范围：\[20,500\]。

 */
  system_disk_size?: number;
  /**节点系统盘磁盘性能，只针对ESSD磁盘生效 */
  system_disk_performance_level?: string;
  /**仅为ECS实例添加标签。 
标签键不可以重复，最大长度为128个字符；标签键和标签值都不能以“aliyun”、“acs:”开头，或包含“https://”、“http://”。 */
  tags?: Tag[];
  /**虚拟交换机ID，取值范围：\[1,20\]。 
> 为保证高可用性，建议选择不同可用区的虚拟交换机。 */
  vswitch_ids?: string[];
  /**SSH登录密码，和`key_pair`二选一。密码规则为8~30个字符，且至少同时包含三项（大小写字母、数字和特殊符号）。 
出于安全性考虑，密码查询结果经过加密处理。 */
  login_password?: string;
  /**密钥对名称，和`login_password`二选一。 
当节点池为托管版节点池时，只支持`key_pair`。 */
  key_pair?: string;
  /**节点公网IP网络计费类型 */
  internet_charge_type?: string;
  /**节点公网IP出带宽最大值，单位为Mbps（Mega bit per second），取值范围：1~100 */
  internet_max_bandwidth_out?: number;
  /**部署集ID。 */
  deploymentset_id?: string;
  /**节点池期望节点数 */
  desired_size?: number;
  /**私有池选项。 */
  private_pool_options?: DescribeClusterNodePoolsResponseNodepoolsScalingGroupPrivatePoolOptions;
};
export interface DescribeClusterNodePoolsResponseNodepoolsStatus{
  /**失败实例数。 */
  failed_nodes?: number;
  /**处于健康状态实例数。 */
  healthy_nodes?: number;
  /**正在创建的节点数。	 */
  initial_nodes?: number;
  /**离线节点数。	 */
  offline_nodes?: number;
  /**正在被移除的节点数。	 */
  removing_nodes?: number;
  /**处于工作状态的节点数。 */
  serving_nodes?: number;
  /**节点池状态，取值： 
- `active`：已激活。
- `scaling`：伸缩中。
- `removing`：节点移除中。
- `deleting`：删除中。
- `updating`：更新中。 */
  state?: string;
  /**节点池内节点数量。 */
  total_nodes?: number;
};
export interface DescribeClusterNodePoolsResponseNodepoolsTeeConfig{
  /**是否开启加密计算集群，取值： 
- `true`：开启。
- `false`：不开启。
 */
  tee_enable?: boolean;
};
export interface DescribeClusterNodePoolsResponseNodepoolsManagementUpgradeConfig{
  /**是否启用自动升级，取值： 
- `true`：启用自动升级。
- `false`：不启用自动升级。
 */
  auto_upgrade?: boolean;
  /**额外节点数量。 */
  surge?: number;
  /**设置额外节点数的比例，和`surge`二选一。 
额外节点数=额外节点数比例×节点数，例如，设置额外节点数比例为50%，存在的节点数为6，那么将产生的额外节点数=50%×6，也就是将生产3个额外节点数。 */
  surge_percentage?: number;
  /**最大不可用节点数量，取值范围：\[1,1000\] 
默认值：1。 */
  max_unavailable?: number;
};
export interface DescribeClusterNodePoolsResponseNodepoolsManagement{
  /**是否开启托管版节点池，取值： 
- `true`：开启托管节点池。
- `false`：不开启托管节点池，只有当`enable=true`时，其他相关配置才生效。
 */
  enable?: boolean;
  /**自动修复，仅当`enable=true`时生效。 
- `true`：自动修复。
- `false`：不自动修复。

 */
  auto_repair?: boolean;
  /**自动升级配置，仅当`enable=true`时生效。 */
  upgrade_config?: DescribeClusterNodePoolsResponseNodepoolsManagementUpgradeConfig;
};
export interface DescribeClusterNodePoolsResponseNodepoolsInterconnectConfig{
  /**边缘增强型节点池绑定的云企业网实例ID(CENID) */
  cen_id?: string;
  /**边缘增强型节点池绑定的云连接网实例ID(CCNID) */
  ccn_id?: string;
  /**边缘增强型节点池绑定的云连接网实例所属的区域 */
  ccn_region_id?: string;
  /**边缘增强型节点池的网络带宽，单位M */
  bandwidth?: number;
  /**边缘增强型节点池的购买时长，单位月 */
  improved_period?: string;
};
export interface DescribeClusterNodePoolsResponseNodepools{
  /**自动伸缩配置。 */
  auto_scaling?: DescribeClusterNodePoolsResponseNodepoolsAutoScaling;
  /**集群相关配置。 */
  kubernetes_config?: DescribeClusterNodePoolsResponseNodepoolsKubernetesConfig;
  /**节点池信息。 */
  nodepool_info?: DescribeClusterNodePoolsResponseNodepoolsNodepoolInfo;
  /**扩容组配置。	 */
  scaling_group?: DescribeClusterNodePoolsResponseNodepoolsScalingGroup;
  /**节点池状态信息。	 */
  status?: DescribeClusterNodePoolsResponseNodepoolsStatus;
  /**加密计算配置。	 */
  tee_config?: DescribeClusterNodePoolsResponseNodepoolsTeeConfig;
  /**托管节点池配置，当前只在专业托管集群中生效。 */
  management?: DescribeClusterNodePoolsResponseNodepoolsManagement;
  /**边缘节点池网络相关的配置。该值只对edge类型的节点池有意义 */
  interconnect_config?: DescribeClusterNodePoolsResponseNodepoolsInterconnectConfig;
  /**边缘节点池的网络类型。basic：基础型；improved：增强型。该值只对edge类型的节点池有意义 */
  interconnect_mode?: string;
  /**边缘节点池允许容纳的最大节点数量. 节点池内可以容纳的最大节点数量，该参数大于等于0。0表示无额外限制（仅受限于集群整体可以容纳的节点数，节点池本身无额外限制）。边缘节点池该参数值往往大于0；ess类型节点池和默认的edge类型节点池该参数值为0 */
  max_nodes?: number;
};
export interface DescribeClusterNodePoolsResponse{
  /**节点池实例列表。 */
  nodepools?: DescribeClusterNodePoolsResponseNodepools[];
};
export interface DescribeClusterNodePoolDetailResponseAutoScaling{
  /**EIP带宽峰值。 */
  eip_bandwidth?: number;
  /**EIP计费类型，取值： 
- `PayByBandwidth`：按固定带宽计费。
- `PayByTraffic`：按使用流量计费。
 */
  eip_internet_charge_type?: string;
  /**是否启用自动伸缩。取值： 
- `true`：开启节点池自动伸缩功能。
- `false`：不开启自动伸缩，当取值为false时，`auto_scaling`内的其他配置参数将不生效。
 */
  enable?: boolean;
  /**是否绑定EIP，取值： 
- `true`：绑定EIP。
- `false`：不绑定EIP。

 */
  is_bond_eip?: boolean;
  /**最大实例数。 */
  max_instances?: number;
  /**最小实例数。 */
  min_instances?: number;
  /**自动伸缩类型，按照自动伸缩实例类型划分。取值： 
- `cpu`：普通实例型。
- `gpu`：GPU实例型。
- `gpushare`：GPU共享型。
- `spot`：抢占式实例型。
 */
  type?: string;
};
export interface DescribeClusterNodePoolDetailResponseKubernetesConfig{
  /**是否在ECS节点上安装云监控，安装后可以在云监控控制台查看所创建ECS实例的监控信息，推荐开启。取值： 
- `true`：在ECS节点上安装云监控。
- `false`：不在ECS节点上安装云监控。 */
  cms_enabled?: boolean;
  /**节点CPU管理策略。当集群版本在1.12.6及以上时支持以下两种策略： 
- `static`：允许为节点上具有某些资源特征Pod增强其CPU亲和性和独占性。
- `none`：表示启用现有的默认CPU亲和性方案。 */
  cpu_policy?: string;
  /**节点标签，为Kubernetes集群节点添加标签。标签定义规则： 
- 标签由区分大小写的键值对组成，您最多可以设置20个标签。
- 标签键不可以重复，最长为64个字符；标签值可以为空，最长为128个字符。标签键和标签值都不能以`aliyun`、`acs:`、`https://`或`http://`开头。更多信息，请参见[Labels and Selectors](https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#syntax-and-character-set)。 */
  labels?: Tag[];
  /**容器运行时名称。 */
  runtime?: string;
  /**容器运行时版本。 */
  runtime_version?: string;
  /**节点污点信息。污点和容忍度（Toleration）相互配合，可以用来避免Pod被分配到不合适的节点上。更多信息，请参见[taint-and-toleration](https://kubernetes.io/zh/docs/concepts/scheduling-eviction/taint-and-toleration/)。 */
  taints?: Taint[];
  /**节点池自定义数据。更多详情，请参见[生成实例自定义数据](~~49121~~)。 */
  user_data?: string;
  /**节点名称由三部分组成：前缀 \+ 节点 IP 地址子串 \+ 后缀： 
- 前缀和后缀均可由“.”分隔的一个或多个部分构成，每个部分可以使用小写字母、数字和“-”，节点名称首尾必须为小写字母和数字。
- IP 地址段长度指截取节点 IP 地址末尾的位数，取值范围 5-12。

例如，节点 IP 地址为：************，指定前缀为 aliyun.com，IP 地址段长度为 5，后缀为 test，则节点名称为aliyun.com00055test。 */
  node_name_mode?: string;
};
export interface DescribeClusterNodePoolDetailResponseNodepoolInfo{
  /**节点池创建时间。 */
  created?: string;
  /**是否为默认节点池，通常一个集群仅有一个默认节点池。取值： `true`：设置为默认节点池。
`false`：不设置为默认节点池。

 */
  is_default?: boolean;
  /**节点池名称。 
命名规则：由数字、汉字、英文字符或短划线（-）组成，长度范围1~63个字符，且不能以短划线（-）开头。 */
  name?: string;
  /**节点池ID。 */
  nodepool_id?: string;
  /**地域ID。 */
  region_id?: string;
  /**资源组ID。 */
  resource_group_id?: string;
  /**节点池类型。 */
  type?: string;
  /**节点池更新时间。 */
  updated?: string;
};
export interface DescribeClusterNodePoolDetailResponseScalingGroupSpotPriceLimit{
  /**抢占式实例规格。 */
  instance_type?: string;
  /**单台实例市场价格。 
<props="china">单位：元/时。

</props>


<props="intl">单位：美元/时。</props>
 */
  price_limit?: string;
};
export interface DescribeClusterNodePoolDetailResponseScalingGroupPrivatePoolOptions{
  /**私有节点池ID。 */
  id?: string;
  /**私有节点池类型，实例启动的私有池容量选项。弹性保障服务或容量预定服务在生效后会生成私有池容量，供实例启动时选择。取值： 
- `Open`：开放模式。将自动匹配开放类型的私有池容量。如果没有符合条件的私有池容量，则使用公共池资源启动。

- `Target`：指定模式。使用指定的私有池容量启动实例，如果该私有池容量不可用，则实例会启动失败。

- `None`：不使用模式。实例启动将不使用私有池容量。 */
  match_criteria?: string;
};
export interface DescribeClusterNodePoolDetailResponseScalingGroup{
  /**节点是否开启自动续费，当`instance_charge_type`取值为`PrePaid`时才生效，取值： 
- `true`：自动续费。
- `false`：不自动续费。
 */
  auto_renew?: boolean;
  /**节点自动续费周期。当`instance_charge_type`取值为`PrePaid`时才生效，且为必选值。 
当`PeriodUnit=Month`时，取值范围：{1, 2, 3, 6, 12}。 */
  auto_renew_period?: number;
  /**节点数据盘类型、大小等配置的组合。 */
  data_disks?: DataDisk[];
  /**自定义镜像ID，可通过`DescribeKubernetesVersionMetadata`查询系统支持的镜像。 
 */
  image_id?: string;
  /**节点池节点付费类型，取值： 
- `PrePaid`：预付费。
- `PostPaid`：按量付费。
 */
  instance_charge_type?: string;
  /**节点实例规格列表，您可以选择多个实例规格作为备选，每个节点创建时，将从第一个规格开始尝试购买，直到创建成功。最终购买的实例规格可能随库存变化而不同。 */
  instance_types?: string[];
  /**多可用区伸缩组ECS实例扩缩容策略。取值： 
- `PRIORITY`：根据您定义的虚拟交换机（VSwitchIds.N）扩缩容。当优先级较高的虚拟交换机所在可用区无法创建ECS实例时，自动使用下一优先级的虚拟交换机创建ECS实例。

- `COST_OPTIMIZED`：按vCPU单价从低到高进行尝试创建。当伸缩配置设置了抢占式计费方式的多实例规格时，优先创建对应抢占式实例。您可以继续通过`CompensateWithOnDemand`参数指定当抢占式实例由于库存等原因无法创建时，是否自动尝试以按量付费的方式创建。

  >`COST_OPTIMIZED`仅在伸缩配置设置了多实例规格或者选用了抢占式实例的情况下生效。

- `BALANCE`：在伸缩组指定的多可用区之间均匀分配ECS实例。如果由于库存不足等原因可用区之间变得不平衡，您可以通过API RebalanceInstances平衡资源。更多信息，请参见[RebalanceInstances](~~71516~~)。

默认值：`PRIORITY`。 */
  multi_az_policy?: string;
  /**伸缩组所需要按量实例个数的最小值，取值范围：\[0,1000\]。当按量实例个数少于该值时，将优先创建按量实例。 */
  on_demand_base_capacity?: number;
  /**伸缩组满足最小按量实例数（`on_demand_base_capacity`）要求后，超出的实例中按量实例应占的比例。取值范围：\[0,100\]。 */
  on_demand_percentage_above_base_capacity?: number;
  /**指定可用实例规格的个数，伸缩组将按成本最低的多个规格均衡创建抢占式实例。取值范围：\[1,10\]。 */
  spot_instance_pools?: number;
  /**是否开启补齐抢占式实例。开启后，当收到抢占式实例将被回收的系统消息时，伸缩组将尝试创建新的实例，替换掉将被回收的抢占式实例。取值： 
- `true`：开启补齐抢占式实例。
- `false`：不开启补齐抢占式实例。

 */
  spot_instance_remedy?: boolean;
  /**当`multi_az_policy`取值为`COST_OPTIMIZED`时，如果因价格、库存等原因无法创建足够的抢占式实例，是否允许自动尝试创建按量实例满足ECS实例数量要求。取值： 
- `true`：允许自动尝试创建按量实例满足ECS实例数量要求。
- `false`：不允许自动尝试创建按量实例满足ECS实例数量要求。 */
  compensate_with_on_demand?: boolean;
  /**节点包年包月时长，当`instance_charge_type`取值为`PrePaid`时才生效且为必选值。 
当`PeriodUnit=Month`时，取值范围：{1, 2 , 3, 6, 12, 24, 36, 48, 60}。 */
  period?: number;
  /**节点付费周期，当`instance_charge_type`取值为`PrePaid`时时候需要指定周期。 
`Month`：目前只支持以月为单位。
 */
  period_unit?: string;
  /**操作系统发行版。取值： 
- `CentOS`
- `AliyunLinux`
- `Windows`
- `WindowsCore` */
  platform?: string;
  /**Worker RAM角色名称，授权ECS实例为集群Woker节点。 */
  ram_policy?: string;
  /**抢占式实例类型，取值： - NoSpot：非抢占式实例。
- SpotWithPriceLimit：设置抢占实例价格上限。
- SpotAsPriceGo：系统自动出价，跟随当前市场实际价格。

更多信息，请参见[抢占式实例](~~157759~~)。
 */
  spot_strategy?: string;
  /**抢占实例市场价格区间配置。 */
  spot_price_limit?: DescribeClusterNodePoolDetailResponseScalingGroupSpotPriceLimit[];
  /**如果指定了RDS实例列表，集群节点ECS会自动加入RDS访问白名单。 */
  rds_instances?: string[];
  /**伸缩组ID。 */
  scaling_group_id?: string;
  /**伸缩组模式，取值： 
- `release`：标准模式，根据申请资源值的使用量，通过创建、释放ECS的方式进行伸缩。
- `recycle`：极速模式，通过创建、停机、启动的方式进行伸缩，提高再次伸缩的速度（停机时计算资源不收费，只收取存储费用，本地盘机型除外）。 */
  scaling_policy?: string;
  /**节点池安全组ID，当节点池绑定了多个安全组时，为`security_group_ids`中的第一个值。 */
  security_group_id?: string;
  /**节点池安全组ID列表。 */
  security_group_ids?: string[];
  /**节点系统盘类型，取值： 
- `cloud_efficiency`：高效云盘。
- `cloud_ssd`：SSD云盘。
 */
  system_disk_category?: string;
  /**节点系统盘大小，单位为GiB。 
取值范围：\[20,500\]。 */
  system_disk_size?: number;
  /**节点系统盘磁盘性能，只针对ESSD磁盘生效。 */
  system_disk_performance_level?: string;
  /**仅为ECS实例添加标签。 
标签键不可以重复，最大长度为128个字符；标签键和标签值都不能以“aliyun”、“acs:”开头，或包含“https://”、“http://”。 */
  tags?: Tag[];
  /**虚拟交换机ID，取值范围：\[1,20\]。 
> 为保证高可用性，建议选择不同可用区的虚拟交换机。 */
  vswitch_ids?: string[];
  /**SSH登录密码，和`key_pair`二选一。密码规则为8~30个字符，且至少同时包含三项（大小写字母、数字和特殊符号）。 
出于安全性考虑，密码查询结果经过加密处理。 */
  login_password?: string;
  /**密钥对名称，和`login_password`二选一。当节点池为托管版节点池时，只支持`key_pair`。 */
  key_pair?: string;
  /**节点公网IP网络计费类型。 */
  internet_charge_type?: string;
  /**节点公网IP出带宽最大值，单位为Mbps（Mega bit per second），取值范围：1~100。 */
  internet_max_bandwidth_out?: number;
  /**部署集ID。 */
  deploymentset_id?: string;
  /**节点池期望节点数。 */
  desired_size?: number;
  /**私有节点池配置。 */
  private_pool_options?: DescribeClusterNodePoolDetailResponseScalingGroupPrivatePoolOptions;
};
export interface DescribeClusterNodePoolDetailResponseStatus{
  /**失败节点数。 */
  failed_nodes?: number;
  /**健康节点数。 */
  healthy_nodes?: number;
  /**正在创建节点数。 */
  initial_nodes?: number;
  /**离线节点数。 */
  offline_nodes?: number;
  /**正在被移除节点数。 */
  removing_nodes?: number;
  /**工作中节点数。 */
  serving_nodes?: number;
  /**节点池状态，取值： 
- `active`：已激活。
- `scaling`：伸缩中。
- `removing`：节点移除中。
- `deleting`：删除中。
- `updating`：更新中。 */
  state?: string;
  /**节点池内总节点数。 */
  total_nodes?: number;
};
export interface DescribeClusterNodePoolDetailResponseTeeConfig{
  /**是否开启加密计算集群，取值： 
- `true`：开启。
- `false`：不开启。
 */
  tee_enable?: boolean;
};
export interface DescribeClusterNodePoolDetailResponseManagementUpgradeConfig{
  /**是否启用自动升级，取值： 
- `true`：启用自动升级。
- `false`：不启用自动升级。
 */
  auto_upgrade?: boolean;
  /**额外节点数量。 */
  surge?: number;
  /**额外节点比例，和`surge`二选一。 */
  surge_percentage?: number;
  /**最大不可用节点数量，取值范围：\[1,1000\]。 
默认值：1。 */
  max_unavailable?: number;
};
export interface DescribeClusterNodePoolDetailResponseManagement{
  /**是否开启托管版节点池，取值： 
- `true`：开启托管节点池。
- `false`：不开启托管节点池，只有当`enable=true`时，其他相关配置才生效。
 */
  enable?: boolean;
  /**自动修复，仅当`enable=true`时生效。 
- `true`：自动修复。
- `false`：不自动修复。
 */
  auto_repair?: boolean;
  /**自动升级配置，仅当`enable=true`时生效。 */
  upgrade_config?: DescribeClusterNodePoolDetailResponseManagementUpgradeConfig;
};
export interface DescribeClusterNodePoolDetailResponseInterconnectConfig{
  /**边缘增强型节点池绑定的云企业网实例ID (CENID)。 */
  cen_id?: string;
  /**边缘增强型节点池绑定的云连接网实例ID (CCNID)。 */
  ccn_id?: string;
  /**边缘增强型节点池绑定的云连接网实例所属的区域。 */
  ccn_region_id?: string;
  /**边缘增强型节点池的网络带宽，单位为Mbps。 */
  bandwidth?: number;
  /**边缘增强型节点池的购买时长，单位为月。 */
  improved_period?: string;
};
export interface DescribeClusterNodePoolDetailResponse{
  /**自动伸缩节点池配置。 */
  auto_scaling?: DescribeClusterNodePoolDetailResponseAutoScaling;
  /**集群相关配置。 */
  kubernetes_config?: DescribeClusterNodePoolDetailResponseKubernetesConfig;
  /**节点池配置。 */
  nodepool_info?: DescribeClusterNodePoolDetailResponseNodepoolInfo;
  /**扩容组配置。 */
  scaling_group?: DescribeClusterNodePoolDetailResponseScalingGroup;
  /**节点池状态配置。 */
  status?: DescribeClusterNodePoolDetailResponseStatus;
  /**加密计算节集群配置。 */
  tee_config?: DescribeClusterNodePoolDetailResponseTeeConfig;
  /**托管节点池配置。 */
  management?: DescribeClusterNodePoolDetailResponseManagement;
  /**边缘节点池网络相关的配置。该值只对edge类型的节点池有意义。 */
  interconnect_config?: DescribeClusterNodePoolDetailResponseInterconnectConfig;
  /**边缘节点池的网络类型。basic：基础型；improved：增强型。该值只对edge类型的节点池有意义。 */
  interconnect_mode?: string;
  /**边缘节点池允许容纳的最大节点数量. 节点池内可以容纳的最大节点数量，该参数大于等于0。0表示无额外限制（仅受限于集群整体可以容纳的节点数，节点池本身无额外限制）。边缘节点池该参数值往往大于0；ess类型节点池和默认的edge类型节点池该参数值为0。 */
  max_nodes?: number;
};
export interface RepairClusterNodePoolBody{
  /**节点列表，如果不指定则表示当前节点池内所有节点 */
  nodes?: string[];
};
export interface RepairClusterNodePoolResponse{
  /**请求ID */
  request_id?: string;
  /**任务ID */
  task_id?: string;
};
export interface UpgradeClusterNodepoolBody{
  /**节点系统镜像ID。 */
  image_id?: string;
  /**节点运行时版本。 */
  runtime_version?: string;
  /**节点Kubernetes版本。 */
  kubernetes_version?: string;
  /**运行时类型，可选值：containerd，docker */
  runtime_type?: string;
};
export interface UpgradeClusterNodepoolResponse{
  /**请求ID。 */
  RequestId?: string;
  /**任务ID。 */
  task_id?: string;
};
export interface UpgradeClusterBody{
  /**组件名称，取值：`k8s`。 */
  component_name?: string;
  /**集群可升级版本。 */
  next_version?: string;
  /**集群当前版本。更多信息，请参见[集群版本](~~185269~~)。 */
  version?: string;
  master_only?: boolean;
};
export interface GetUpgradeStatusResponseUpgradeTask{
  /**升级任务状态。取值： - `running`: 执行中。
- `Success`: 执行成功。
- `Failed`: 执行失败。

 */
  status?: string;
  /**升级任务描述信息。 */
  message?: string;
};
export interface GetUpgradeStatusResponse{
  /**集群升级中的错误信息。 */
  error_message?: string;
  /**预检查返回ID。 */
  precheck_report_id?: string;
  /**集群目前升级的状态。取值： 
- `success` : 升级成功。
- `fail` : 升级失败。
- `pause` : 升级暂停。
- `running` : 升级进行中。 */
  status?: string;
  /**集群目前升级的阶段。取值： 
- `not_start` : 暂未开始。
- `prechecking` : 预检查中。
- `upgrading`:  升级中。
- `pause` : 暂停中。
- `success` : 升级成功。 */
  upgrade_step?: string;
  /**升级任务详情。 */
  upgrade_task?: GetUpgradeStatusResponseUpgradeTask;
};
export interface CreateEdgeMachineBody{
  /**云原生一体机`hostname`。 
> 云原生一体机激活后会自动修改主机的`hostname`，`hostname`以产品型号为前缀，后面是一个随机字符串。 */
  hostname: string;
  /**云原生一体机SN序列号 */
  sn: string;
  /**云原生一体机的型号 */
  model: string;
};
export interface CreateEdgeMachineResponse{
  /**请求ID */
  request_id?: string;
  /**云原生一体机ID */
  edge_machine_id?: string;
};
export interface DescribeEdgeMachineActiveProcessResponse{
  /**请求ID */
  request_id?: string;
  /**激活状态 */
  state?: string;
  /**激活步骤 */
  step?: string;
  /**激活进度 */
  progress?: number;
  /**激活进度列表 */
  logs?: string;
};
export interface DescribeEdgeMachineModelsResponseModels{
  /**是否管理Docker Runtime */
  manage_runtime?: number;
  /**CPU架构 */
  cpu_arch?: string;
  /**CPU核数 */
  cpu?: number;
  /**内存，单位为GB */
  memory?: number;
  /**云原生一体机ID */
  model_id?: string;
  /**创建时间 */
  created?: string;
  /**描述 */
  description?: string;
  /**云原生一体机型号 */
  model?: string;
};
export interface DescribeEdgeMachineModelsResponse{
  /**云原生一体机型号列表信息 */
  models?: DescribeEdgeMachineModelsResponseModels[];
};
export interface DescribeEdgeMachinesResponseEdgeMachines{
  /**设备ID */
  edge_machine_id?: string;
  /**创建时间 */
  created?: string;
  /**更新时间 */
  updated?: string;
  /**机器名称 */
  name?: string;
  /**云原生一体机`hostname` */
  hostname?: string;
  /**SN序列号 */
  sn?: string;
  /**云原生一体机型号 */
  model?: string;
  /**生命周期 */
  life_state?: string;
  /**在线状态 */
  online_state?: string;
  /**激活时间 */
  active_time?: string;
};
export interface DescribeEdgeMachinesResponsePageInfo{
  /**页码。 
默认值：1。 */
  page_number?: number;
  /**每页显示的记录数。 
默认值：10。 */
  page_size?: number;
  /**总数 */
  total_count?: number;
};
export interface DescribeEdgeMachinesResponse{
  /**云原生一体机列表信息 */
  edge_machines?: DescribeEdgeMachinesResponseEdgeMachines[];
  /**分页信息 */
  page_info?: DescribeEdgeMachinesResponsePageInfo;
};
export interface DescribeEdgeMachineTunnelConfigDetailResponse{
  /**云原生一体机型号 */
  model?: string;
  /**云原生一体机SN序列号 */
  sn?: string;
  /**Tunnel后端链接 */
  tunnel_endpoint?: string;
  /**Token */
  token?: string;
  /**Product Key */
  product_key?: string;
  /**设备名 */
  device_name?: string;
  /**Request ID */
  request_id?: string;
};
export interface EdgeClusterAddEdgeMachineBody{
  /**可选项。 */
  options?: string;
  /**超时时间。单位：s。 */
  expired?: number;
  /**节点池ID。 */
  nodepool_id: string;
};
export interface EdgeClusterAddEdgeMachineResponse{
  /**请求ID。 */
  request_id?: string;
  /**云原生一体机ID。 */
  edge_machine_id?: string;
};
export interface CreateTemplateBody{
  /**模板名称。 
命名规则：由数字、汉字、英文字符或短划线（-）组成，长度范围1~63个字符，且不能以短划线（-）开头。 */
  name: string;
  /**YAML格式的模板内容。 */
  template: string;
  /**编排模板标签。 */
  tags?: string;
  /**编排模板描述。 */
  description?: string;
  /**模板类型，取值可以是任意值。 
- 当取值是`kubernetes`时将在控制台的编排模板页面展示该模板。

- 当取值是`compose`时将不在控制台展示。

推荐设置为`kubernetes`。

默认值：`compose`。 */
  template_type?: string;
};
export interface CreateTemplateResponse{
  /**编排模板ID。 */
  template_id?: string;
};
export interface UpdateTemplateBody{
  /**部署模板描述信息。 */
  description?: string;
  /**部署模板名称。 */
  name?: string;
  /**部署模板标签。 */
  tags?: string;
  /**YAML格式的模板内容。 */
  template?: string;
  /**模板类型，值可以是任意值。 
- 当取值是`kubernetes`时将在控制台的编排模板页面展示该模板。

- 当取值是`compose`时将在控制台Swarm集群页面显示该模板（已废弃）。
 */
  template_type?: string;
};
export interface DescribeTemplatesResponseTemplates{
  /**用户部署模板的访问权限，取值： 
- `private`：私有的。
- `public`：公共的。
- `shared`：可共享的。

默认值：`private`。 */
  acl?: string;
  /**部署模板ID。 */
  id?: string;
  /**部署模板名称。 */
  name?: string;
  /**部署模板描述信息。 */
  description?: string;
  /**部署模板的标签，如果不显式指定，默认为模板名称。 */
  tags?: string;
  /**YAML格式的模板内容。 */
  template?: string;
  /**模板类型，值可以是任意值。 
- 当取值是`kubernetes`时将在控制台的编排模板页面展示该模板。

- 当取值是`compose`时将在控制台Swarm集群页面显示该模板（已废弃）。 */
  template_type?: string;
  /**部署模板创建时间。 */
  created?: string;
  /**部署模板更新时间。 */
  updated?: string;
  /**模板关联的父模板ID，用于实现模板多版本功能（同一模板的不同版本拥有相同的`template_with_hist_id`值）。 */
  template_with_hist_id?: string;
};
export interface DescribeTemplatesResponsePageInfo{
  /**展示当前页数。 */
  page_number?: number;
  /**单页最大数据条数。 */
  page_size?: number;
  /**结果总数。 */
  total_count?: number;
};
export interface DescribeTemplatesResponse{
  /**模板列表。 */
  templates?: DescribeTemplatesResponseTemplates[];
  /**分页信息。 */
  page_info?: DescribeTemplatesResponsePageInfo;
};
export interface DescribeTemplateAttributeResponse{
  /**编排模板ID，每次变更都会有一个模板ID。 */
  id?: string;
  /**编排模板访问权限。 */
  acl?: string;
  /**编排模板名称。 */
  name?: string;
  /**编排模板YAML内容。 */
  template?: string;
  /**模板类型，其值可以是任意值。 
- 当取值是`kubernetes`时将在控制台的编排模板页面展示该模板。

- 当取值是`compose`时将在控制台Swarm集群页面显示该模板（已废弃）。

- 当取值非`kubernetes`时在控制台的编排模板页面将不会展示该模板，推荐使用`kubernetes`。

默认值：`kubernetes`。
 */
  template_type?: string;
  /**编排模板描述信息。 */
  description?: string;
  /**部署模板的标签。 */
  tags?: string;
  /**编排模板唯一ID，不随模板更新而改变。 */
  template_with_hist_id?: string;
  /**编排模板创建时间。 */
  created?: string;
  /**编排模板更新时间。 */
  updated?: string;
};
export interface CreateTriggerBody{
  /**集群ID。 */
  cluster_id: string;
  /**触发器项目名称。 
由应用所在命名空间及应用名称组成，格式为`${namespace}/${name}`。

取值示例：`default/test-app`。 */
  project_id: string;
  /**触发器行为，取值： 
`redeploy`：重新部署`project_id`中定义的资源。 */
  action: string;
  /**触发器类型。取值： 
- `deployment`：针对无状态应用的触发器。 

- `application`：针对应用中心应用的触发器。

默认值：`deployment`。 */
  type?: string;
};
export interface CreateTriggerResponse{
  /**触发器ID。 */
  id?: string;
  /**集群ID。 */
  cluster_id?: string;
  /**触发器项目名称。 */
  project_id?: string;
  /**触发器类型。默认值为 deployment 。 */
  type?: string;
  /**触发器行为。例如，`redeploy`：重新部署。 */
  action?: string;
};
export interface DescribeTriggerResponse{
  /**触发器ID。 */
  id?: string;
  /**触发器名称。 */
  name?: string;
  /**集群ID。 */
  cluster_id?: string;
  /**触发器项目名称。 
由应用所在命名空间及应用名称组成，格式为`${namespace}/${name}`，取值示例：default/test-app。 */
  project_id?: string;
  /**触发器类型。 
取值：

- `deployment`：针对无状态应用的触发器。 

- `application`：针对应用中心应用的触发器。

默认值：`deployment`。 */
  type?: string;
  /**触发器行为，取值： 
`redeploy`: 重新部署project_id中定义的资源。 */
  action?: string;
  /**Token信息。 */
  token?: string;
};
export interface CreateKubernetesTriggerBody{
  /**集群ID。 */
  cluster_id: string;
  /**触发器项目名称。 
由应用所在命名空间及应用名称组成，格式为`${namespace}/${name}`。

取值示例：`default/test-app`。 */
  project_id: string;
  /**触发器行为，取值： 
`redeploy`：重新部署`project_id`中定义的资源。 */
  action: string;
  /**触发器类型。取值： 
- `deployment`：针对无状态应用的触发器。 

- `application`：针对应用中心应用的触发器。

默认值：`deployment`。 */
  type?: string;
};
export interface CreateKubernetesTriggerResponse{
  /**触发器ID。 */
  id?: string;
  /**集群ID。 */
  cluster_id?: string;
  /**触发器项目名称。 */
  project_id?: string;
  /**触发器类型。 
取值：

- `deployment`：针对无状态应用的触发器。 

- `application`：针对应用中心应用的触发器。
 */
  type?: string;
  /**触发器行为。例如，`redeploy`：重新部署。 */
  action?: string;
};
export interface GetKubernetesTriggerResponse{
  /**触发器ID。 */
  id?: string;
  /**触发器名称。 */
  name?: string;
  /**集群ID。 */
  cluster_id?: string;
  /**触发器项目名称。 
由应用所在命名空间及应用名称组成，格式为`${namespace}/${name}`，取值示例：default/test-app。 */
  project_id?: string;
  /**触发器类型。 
取值：

- `deployment`：针对无状态应用的触发器。 

- `application`：针对应用中心应用的触发器。

默认值：`deployment`。 */
  type?: string;
  /**触发器行为，取值： 
`redeploy`: 重新部署project_id中定义的资源。 */
  action?: string;
  /**Token */
  token?: string;
};
export interface InstallClusterAddonsBody{
  /**组件名称。 */
  name: string;
  /**组件版本。 
> 可以API接口[DescribeClusterAddonsVersion](~~197434~~)通过组件列表查询版本号。 */
  version: string;
  /**组件自定义参数，使用JSON字符串编码。 */
  config?: string;
};
export interface UnInstallClusterAddonsBody{
  /**组件名称。 */
  name?: string;
};
export interface DescribeClusterAddonMetadataResponse{
  /**组件名称 */
  name?: string;
  /**组件版本 */
  version?: string;
  /**组件参数Schema */
  config_schema?: string;
};
export interface ModifyClusterAddonBody{
  /**自定义参数设置。 */
  config?: string;
};
export interface UpgradeClusterAddonsBody{
  /**组件名称。 */
  component_name: string;
  /**可升级版本。调用`DescribeClusterAddonsVersion`参数可以查看可以升级的版本。 */
  next_version: string;
  /**当前版本。 */
  version?: string;
  /**组件自定义参数，使用JSON字符串编码。 */
  config?: string;
  policy?: string;
};
export interface DescribeAddonsResponseComponentGroupsItems{
  /**组件名称。 */
  name?: string;
};
export interface DescribeAddonsResponseComponentGroups{
  /**组件分组名称。 */
  group_name?: string;
  /**该分组内包含的组件名称。 */
  items?: DescribeAddonsResponseComponentGroupsItems[];
};
export interface DescribeAddonsResponse{
  /**组件分组列表。 */
  ComponentGroups?: DescribeAddonsResponseComponentGroups[];
  /**标准组件。 */
  StandardComponents?: any;
};
export interface UntagResourcesResponse{
  /**请求id。 */
  RequestId?: string;
};
export interface TagResourcesBody{
  /**资源ID列表。 */
  resource_ids: string[];
  /**资源类型定义。取值范围：只支持`CLUSTER`这一种资源类型。 */
  resource_type: string;
  /**资源所属的地域ID。 */
  region_id: string;
  /**资源的标签键值对。数组长度范围：\[1,20\]。注意： 
- 一旦传值，值不允许为空字符串，最多支持128个字符。
- 不能以`aliyun`和`acs:`开头。
- 不能包含`http://`或者`https://`。 */
  tags: Tag[];
};
export interface TagResourcesResponse{
  /**请求id。 */
  RequestId?: string;
};
export interface ListTagResourcesResponseTagResourcesTagResource{
  /**标签的名称。 */
  tag_key?: string;
  /**标签值。 */
  tag_value?: string;
  /**资源ID。 */
  resource_id?: string;
  /**资源类型。更多信息，请参见[标签](~~110425~~)。 */
  resource_type?: string;
};
export interface ListTagResourcesResponseTagResources{
  /**标签资源。 




 */
  tag_resource?: ListTagResourcesResponseTagResourcesTagResource[];
};
export interface ListTagResourcesResponse{
  /**下一个查询开始的令牌。 
 */
  next_token?: string;
  /**请求ID。 */
  request_id?: string;
  /**标签资源集。 */
  tag_resources?: ListTagResourcesResponseTagResources;
};
export interface ModifyClusterConfigurationBodyCustomizeConfigConfigs{
  /**配置项名字。 */
  key?: string;
  /**配置项值。 */
  value?: string;
};
export interface ModifyClusterConfigurationBodyCustomizeConfig{
  /**组件名称。 */
  name?: string;
  /**自定义配置信息。 */
  configs?: ModifyClusterConfigurationBodyCustomizeConfigConfigs[];
};
export interface ModifyClusterConfigurationBody{
  /**自定义配置。 */
  customize_config?: ModifyClusterConfigurationBodyCustomizeConfig[];
};
export interface DeletePolicyInstanceResponse{
  /**策略实例列表。 */
  instances?: string[];
};
export interface ModifyPolicyInstanceBody{
  /**规则治理动作，取值： 
- `deny`：拦截违规部署
- `warn`：告警 */
  action?: string;
  /**策略规则实例ID */
  instance_name?: string;
  /**限制策略实施的命名空间，为空时表示所有命名空间 */
  namespaces?: string[];
  /**当前规则实例的配置参数。更多参数配置规则，请参见[容器安全策略规则库说明](~~359819~~)。 
 */
  parameters?: any;
};
export interface ModifyPolicyInstanceResponse{
  /**已更新的实例列表 */
  instances?: string[];
};
export interface DescribePolicyDetailsResponse{
  /**策略治理规则名称 */
  name?: string;
  /**规则模板类型 */
  category?: string;
  /**规则模板描述 */
  description?: string;
  /**规则治理动作，取值： - `enforce`：拦截违规部署
- `inform`：告警 */
  action?: string;
  /**规则治理等级 */
  severity?: string;
  /**规则模板详情 */
  template?: string;
  /**是否需要配置策略，取值：  - 0：表示需要参数配置
- 1：表示无需参数配置 */
  no_config?: number;
  /**是否删除标志，取值： - 0：表示未删除。
- 1：表示删除。 */
  is_deleted?: number;
};
export interface DescribePolicyGovernanceInClusterResponseOnState{
  /**当前开启的策略种类计数 */
  enabled_count?: number;
  /**该等级下策略种类总数 */
  total?: number;
  /**策略治理等级 */
  severity?: string;
};
export interface DescribePolicyGovernanceInClusterResponseAdmitLogLog{
  /**策略治理审计日志信息 */
  msg?: string;
  /**目标集群ID */
  cluster_id?: string;
  /**策略类型名称 */
  constraint_kind?: string;
  /**目标资源名称 */
  resource_name?: string;
  /**目标资源类型 */
  resource_kind?: string;
  /**目标资源命名空间 */
  resource_namespace?: string;
};
export interface DescribePolicyGovernanceInClusterResponseAdmitLog{
  /**查询结果的状态，取值： * `Complete`：查询已经完成，返回结果为完整结果。
* `Incomplete`：查询已经完成，返回结果为不完整结果，需要重复请求以获得完整结果。 */
  progress?: string;
  /**当前查询到的日志总数 */
  count?: number;
  /**策略治理审计日志内容 */
  log?: DescribePolicyGovernanceInClusterResponseAdmitLogLog;
};
export interface DescribePolicyGovernanceInClusterResponseTotalViolationsDeny{
  /**策略治理等级 */
  severity?: string;
  /**被拦截的事件计数 */
  violations?: number;
};
export interface DescribePolicyGovernanceInClusterResponseTotalViolationsWarn{
  /**策略治理等级 */
  severity?: string;
  /**告警的事件计数 */
  violations?: number;
};
export interface DescribePolicyGovernanceInClusterResponseTotalViolations{
  /**被拦截的不同治理等级的违规计数统计 */
  deny?: DescribePolicyGovernanceInClusterResponseTotalViolationsDeny;
  /**告警模式下不同治理等级的违规计数统计 */
  warn?: DescribePolicyGovernanceInClusterResponseTotalViolationsWarn;
};
export interface DescribePolicyGovernanceInClusterResponseViolationsDeny{
  /**策略名称 */
  policyName?: string;
  /**策略描述 */
  policyDescription?: string;
  /**集群中对应规则类型下被拦截的违规计数统计 */
  violations?: number;
  /**策略治理等级 */
  severity?: string;
};
export interface DescribePolicyGovernanceInClusterResponseViolationsWarn{
  /**策略名称 */
  policyName?: string;
  /**策略描述 */
  policyDescription?: string;
  /**集群中对应规则类型下被告警的违规计数统计 */
  violations?: number;
  /**策略治理等级 */
  severity?: string;
};
export interface DescribePolicyGovernanceInClusterResponseViolations{
  /**被拦截的不同策略类型的审计计数 */
  deny?: DescribePolicyGovernanceInClusterResponseViolationsDeny;
  /**告警模式下不同治理等级的违规计数统计 */
  warn?: DescribePolicyGovernanceInClusterResponseViolationsWarn;
};
export interface DescribePolicyGovernanceInClusterResponse{
  /**当前集群中开启的不同等级策略计数统计 */
  on_state?: DescribePolicyGovernanceInClusterResponseOnState[];
  /**集群当前策略治理审计日志 */
  admit_log?: DescribePolicyGovernanceInClusterResponseAdmitLog;
  /**集群中当前被拦截和告警两种处理类型下不同治理等级的违规计数。 */
  totalViolations?: DescribePolicyGovernanceInClusterResponseTotalViolations;
  /**集群中针对不同策略类型的拦截和告警的审计计数统计列表 */
  violations?: DescribePolicyGovernanceInClusterResponseViolations;
};
export interface DescribePolicyInstancesResponse{
  /**策略实例实施者UID */
  ali_uid?: string;
  /**目标集群ID */
  cluster_id?: string;
  /**规则实例名称 */
  instance_name?: string;
  /**策略治理规则名称 */
  policy_name?: string;
  /**策略类型名称 */
  policy_category?: string;
  /**规则模板描述 */
  policy_description?: string;
  /**当前规则实例的配置参数 */
  policy_parameters?: string;
  /**规则实例治理等级 */
  policy_severity?: string;
  /**策略实例实施范围： 
默认"*"代表集群所有命名空间。

否则返回作用Namespaces名称，多个Namespaces以逗号（,）分隔。 */
  policy_scope?: string;
  /**规则治理动作，取值： 
- `deny`：拦截违规部署

- `warn`：告警 */
  policy_action?: string;
};
export interface DescribePolicyInstancesStatusResponsePolicyInstances{
  /**策略类型 */
  policy_category?: string;
  /**策略名称 */
  policy_name?: string;
  /**策略描述 */
  policy_description?: string;
  /**策略治理等级 */
  policy_severity?: string;
  /**已部署的策略实例计数，如果字段为空说明未部署该类型策略实例。 */
  policy_instances_count?: number;
};
export interface DescribePolicyInstancesStatusResponse{
  /**不同策略类型下的策略实例计数列表 */
  policy_instances?: DescribePolicyInstancesStatusResponsePolicyInstances[];
  /**集群中当前部署的不同治理等级的策略实例计数 */
  instances_severity_count?: any;
};
export interface DeployPolicyInstanceBody{
  /**规则治理动作，取值： 
- `deny`：拦截违规部署
- `warn`：告警 */
  action?: string;
  /**限制策略实施的命名空间，为空时表示所有命名空间。 */
  namespaces?: string[];
  /**当前规则实例的配置参数。 */
  parameters?: any;
};
export interface DeployPolicyInstanceResponse{
  /**策略实例列表。 */
  instances?: string[];
};
export interface FixNodePoolVulsBodyRolloutPolicy{
  /**轮转修复时的最大并行度，最小值为1，最大值为节点池节点数量。 */
  max_parallelism?: number;
};
export interface FixNodePoolVulsBody{
  /**漏洞列表。 */
  vuls?: string[];
  /**待修复的节点名称列表。 */
  nodes?: string[];
  /**轮转修复策略。 */
  rollout_policy?: FixNodePoolVulsBodyRolloutPolicy;
};
export interface FixNodePoolVulsResponse{
  /**修复任务ID。 */
  task_id?: string;
};
export interface DescribeNodePoolVulsResponseVulRecordsVulList{
  /**漏洞名称。 */
  name?: string;
  /**漏洞别名。 */
  alias_name?: string;
  /**漏洞等级。 */
  necessity?: string;
  /**漏洞对应的CVE列表。 */
  cve_list?: string[];
};
export interface DescribeNodePoolVulsResponseVulRecords{
  /**节点实例ID。 */
  instance_id?: string;
  /**漏洞列表。 */
  vul_list?: DescribeNodePoolVulsResponseVulRecordsVulList[];
  /**节点名称，集群内节点标识。 */
  node_name?: string;
};
export interface DescribeNodePoolVulsResponse{
  /**节点池漏洞列表。 */
  vul_records?: DescribeNodePoolVulsResponseVulRecords[];
  /**是否已购买云安全CVE修复服务 */
  vuls_fix_service_purchased?: boolean;
};
export interface DescribeClusterEventsResponseEventsData{
  /**事件级别。 */
  level?: string;
  /**事件状态。 */
  reason?: string;
  /**事件详情。 */
  message?: string;
};
export interface DescribeClusterEventsResponseEvents{
  /**事件ID。 */
  event_id?: string;
  /**事件类型。 */
  type?: string;
  /**事件源。 */
  source?: string;
  /**事件关联的操作对象。 */
  subject?: string;
  /**事件开始时间。 */
  time?: string;
  /**集群ID。 */
  cluster_id?: string;
  /**事件描述。 */
  data?: DescribeClusterEventsResponseEventsData;
};
export interface DescribeClusterEventsResponsePageInfo{
  /**每页大小，取值范围1-50。 默认值：50。 */
  page_size?: number;
  /**分页页数。 */
  page_number?: number;
  /**结果总数。 */
  total_count?: number;
};
export interface DescribeClusterEventsResponse{
  /**事件列表。 */
  events?: DescribeClusterEventsResponseEvents[];
  /**分页信息。 */
  page_info?: DescribeClusterEventsResponsePageInfo;
};
export interface DescribeClusterTasksResponsePageInfo{
  /**每页数量。 */
  page_size?: number;
  /**页数。 */
  page_number?: number;
  /**结果总数。 */
  total_count?: number;
};
export interface DescribeClusterTasksResponseTasksError{
  /**错误信息。 */
  message?: string;
  /**错误码。 */
  code?: string;
};
export interface DescribeClusterTasksResponseTasks{
  /**任务ID。 */
  task_id?: string;
  /**任务类型。 */
  task_type?: string;
  /**任务状态。 */
  state?: string;
  /**创建时间。 */
  created?: string;
  /**更新时间。 */
  updated?: string;
  /**任务错误信息。 */
  error?: DescribeClusterTasksResponseTasksError;
};
export interface DescribeClusterTasksResponse{
  /**分页信息。 */
  page_info?: DescribeClusterTasksResponsePageInfo;
  /**任务数组。 */
  tasks?: DescribeClusterTasksResponseTasks[];
  /**请求ID。 */
  requestId?: string;
};
export interface ModifyNodePoolNodeConfigBodyKubeletConfig{
  /**镜像仓库的QPS上限。 */
  registryPullQPS?: number;
  /**突发性镜像拉取的个数上限。 */
  registryBurst?: number;
  /**每秒可生成的事件数量。 */
  eventRecordQPS?: number;
  /**事件记录的个数的突发峰值上限。 */
  eventBurst?: number;
  /**与API Server通信的每秒查询个数。 */
  kubeAPIQPS?: number;
  /**每秒发送到API Server的突发请求数量上限。 */
  kubeAPIBurst?: number;
  /**是否逐一拉取镜像。 */
  serializeImagePulls?: boolean;
  /**CPU管理器策略。 */
  cpuManagerPolicy?: string;
  /**触发Pod驱逐操作的一组硬性门限。 */
  evictionHard?: any;
  /**设置一组驱逐阈值。 */
  evictionSoft?: any;
  /**设置一组驱逐宽限期。 */
  evictionSoftGracePeriod?: any;
  /**系统预留的资源配置。 */
  systemReserved?: any;
  /**kubernetes系统预留的资源配置。 */
  kubeReserved?: any;
};
export interface ModifyNodePoolNodeConfigBodyRollingPolicy{
  /**最大不可用节点数。 */
  max_parallelism?: number;
};
export interface ModifyNodePoolNodeConfigBody{
  /**Kubelet参数配置。 */
  kubelet_config?: ModifyNodePoolNodeConfigBodyKubeletConfig;
  /**轮转配置。 */
  rolling_policy?: ModifyNodePoolNodeConfigBodyRollingPolicy;
};
export interface ModifyNodePoolNodeConfigResponse{
  /**请求ID。 */
  request_id?: string;
  /**任务ID。 */
  task_id?: string;
  /**节点池ID。 */
  nodepool_id?: string;
};
export interface RemoveNodePoolNodesResponse{
  /**请求ID。 */
  request_id?: string;
  /**任务ID。 */
  task_id?: string;
};
export interface SyncClusterNodePoolResponse{
  /**请求ID。 */
  RequestId?: string;
};
export interface DescribeSubaccountK8sClusterUserConfigResponse{
  /**集群访问配置。关于如何查看访问集群配置信息，请参见[配置集群凭据](~~86494~~)。 */
  config: string;
  /**KubeConfig的过期时间。格式：RFC3339格式的UTC时间。 */
  expiration?: string;
};
export interface ScanClusterVulsResponse{
  /**请求ID。 */
  request_id?: string;
  /**任务ID。 */
  task_id?: string;
};
export interface DescribeClusterVulsResponseVulRecords{
  /**节点池名称。 */
  nodepool_name?: string;
  /**节点池ID。 */
  nodepool_id?: string;
  /**具有该漏洞的节点数量。 */
  node_count?: number;
  /**漏洞名称。 */
  vul_name?: string;
  /**漏洞别名。 */
  vul_alias_name?: string;
  /**漏洞类型。 */
  vul_type?: string;
  /**漏洞等级。 */
  necessity?: string;
  /**CVE列表。 */
  cve_list?: string[];
};
export interface DescribeClusterVulsResponse{
  /**漏洞列表。 */
  vul_records?: DescribeClusterVulsResponseVulRecords[];
};
export interface AttachInstancesToNodePoolBody{
  /**待添加的实例ID列表。 */
  instances?: string[];
  /**待添加实例的SSH登录密码。 */
  password?: string;
  /**是否将容器数据和镜像存储在数据盘中。取值： 
- `true`：将容器数据和镜像存储在数据盘。

- `false`：不将容器数据和镜像存储在数据盘。

默认值：`false`。


数据盘挂载规则：

- 如果ECS已挂载数据盘，且最后一块数据盘的文件系统未初始化，系统会自动将该数据盘格式化为EXT4，用来存放内容/var/lib/docker、/var/lib/kubelet。
- 如果ECS未挂载数据盘，则不会挂载新的数据盘。
>当选择将数据存储至数据盘并且ECS已挂载数据盘，数据盘内原有数据将丢失，请注意备份数据。 */
  format_disk?: boolean;
  /**是否保留原实例名称。取值： 
- `true`：保留实例名称。

- `false`：不保留实例名称。

默认值：`true`。 */
  keep_instance_name?: boolean;
};
export interface AttachInstancesToNodePoolResponse{
  /**请求ID。 */
  request_id?: string;
  /**任务ID。 */
  task_id?: string;
};
export interface DescribeClusterAddonInstanceResponse{
  /**组件名称。 */
  name?: string;
  /**组件版本。 */
  version?: string;
  /**组件状态： 
- initial：安装中
- active：已安装
- unhealthy：状态异常
- upgrading：升级中
- updating：变更中
- deleting：卸载中
- deleted：已删除 */
  state?: string;
  /**组件配置。 */
  config?: string;
};
export interface CheckControlPlaneLogEnableResponse{
  /**控制平面组件日志对应存储的SLS Project名称。 
默认值：k8s-log-$集群ID。 */
  log_project?: string;
  /**日志在SLS logstore里的数据保存时间。取值范围：1~3000，单位：天。 
默认值：30天。 */
  log_ttl?: string;
  /**阿里云账号ID。 */
  aliuid?: string;
  /**当前开启控制平面日志的组件列表。 */
  components: string[];
};
export interface UpdateControlPlaneLogBody{
  /**控制平面组件日志对应存储的SLS Project名称。 
默认值：k8s-log-$集群ID。 */
  log_project?: string;
  /**日志在SLS logstore里的数据保存时间。取值范围：1~3000，单位：天。 
默认值：30天。 */
  log_ttl?: string;
  /**阿里云账号ID。 */
  aliuid?: string;
  /**当前开启控制平面日志的组件列表。 */
  components?: string[];
};
export default interface CS{
  /**查询用户操作事件。 */
  DescribeEvents:{
    request:{
      params:{
        /**集群ID。 */
        cluster_id?: string;
        /**事件类型。取值： - `cluster_create`：创建集群。
- `cluster_scaleout`：扩容集群。
- `cluster_attach`：添加已有节点。
- `cluster_delete`：删除集群。
- `cluster_upgrade`：升级集群。
- `cluster_migrate`：迁移集群。
- `cluster_node_delete`：移除节点。
- `cluster_node_drain`：清空节点。
- `cluster_modify`：修改集群。
- `cluster_configuration_modify`：修改集群管控配置。
- `cluster_addon_install`：安装组件。
- `cluster_addon_upgrade`：升级组件。
- `cluster_addon_uninstall`：卸载组件。
- `runtime_upgrade`：升级运行时。
- `nodepool_upgrade`：升级节点池。
- `nodepool_update`：更新节点池。 */
        type?: string;
        /**每页显示数量。 */
        page_size?: number;
        /**分页查询页数。 */
        page_number?: number;
      };
    };
    response: DescribeEventsResponse;
  };
  DeleteAlertContact:{
    request:{
    };
    response: unknown;
  };
  DeleteAlertContactGroup:{
    request:{
    };
    response: unknown;
  };
  StartAlert:{
    request:{
      params:{
        /**集群ID。您可以通过调用ListClusters获取集群ID。 */
        ClusterId: string;
      };
    };
    response: StartAlertResponse;
  };
  /**停止ACK报警中心中报警规则，停止整个报警规则集分组、单个报警规则。 */
  StopAlert:{
    request:{
      params:{
        /**集群ID。 
您可以通过调用[ListClusters](~~87116~~)获取集群ID。 */
        ClusterId: string;
      };
    };
    response: StopAlertResponse;
  };
  UpdateContactGroupForAlert:{
    request:{
      params:{
        /**集群ID，可以调用ListCluster接口获取。 */
        ClusterId?: string;
      };
    };
    response: unknown;
  };
  /**调用OpenAckService接口开通容器服务ACK。 */
  OpenAckService:{
    request:{
      params:{
        /**要开通的服务类型。取值： 
- `propayasgo`：容器服务ACK Pro托管版。
- `edgepayasgo`：边缘容器服务。
- `gspayasgo`：基因计算服务。 */
        type?: string;
      };
    };
    response: OpenAckServiceResponse;
  };
  /**调用GrantPermissions全量更新RAM用户集群授权信息。 */
  GrantPermissions:{
    request:{
      params:{
        /**RAM用户的ID。 */
        uid: string;
      };
      body: GrantPermissionsBody[];
    };
    response: unknown;
  };
  /**查询指定RAM用户的集群授权信息。 */
  DescribeUserPermission:{
    request:{
      params:{
        /**指定RAM用户或RAM角色的ID。 
> 当为RAM角色授权时，使用RAM角色的ID。 */
        uid: string;
      };
    };
    response: DescribeUserPermissionResponse[];
  };
  /**调用StartWorkflow创建一个新的基因工作流。 */
  StartWorkflow:{
    request:{
      body: StartWorkflowBody;
    };
    response: StartWorkflowResponse;
  };
  /**调用RemoveWorkflow删除某个指定工作流。 */
  RemoveWorkflow:{
    request:{
      params:{
        /**工作流名称。 */
        workflowName: string;
      };
    };
    response: unknown;
  };
  /**调用CancelWorkflow取消正在运行中的工作流。 */
  CancelWorkflow:{
    request:{
      params:{
        /**工作流名称。 */
        workflowName: string;
      };
      body: CancelWorkflowBody;
    };
    response: unknown;
  };
  /**调用DescribeWorkflow查询单个工作流的详细信息。 */
  DescirbeWorkflow:{
    request:{
      params:{
        /**工作流名称 */
        workflowName: string;
      };
    };
    response: DescirbeWorkflowResponse;
  };
  /**调用DescribeWorkflows查询已创建的所有工作流。 */
  DescribeWorkflows:{
    request:{
    };
    response: DescribeWorkflowsResponse;
  };
  /**创建ACK集群（ACK 专有版集群、ACK托管版集群、ASK集群，ACK边缘托管版集群以及注册集群）。 */
  CreateCluster:{
    request:{
      body: CreateClusterBody;
    };
    response: CreateClusterResponse;
  };
  /**根据集群 ID 删除集群实例，并释放集群所有节点资源。 */
  DeleteCluster:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**是否保留所有资源。如果设置该值为`true`，则会忽略`retain_resources`。 
- `true`：保留所有资源。
- `false`：不保留所有资源。

默认值：`false`。 */
        retain_all_resources?: boolean;
        /**是否保留SLB，取值： 
- `true`：保留创建的SLB资源。
- `false`：不保留创建的SLB资源。

默认值：`false`。 */
        keep_slb?: boolean;
        /**资源列表。删除集群时如果需要保留资源，则需要提供对应资源的ID。 */
        retain_resources?: string[];
      };
    };
    response: DeleteClusterResponse;
  };
  /**根据集群ID，扩容集群。 */
  ScaleOutCluster:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: ScaleOutClusterBody;
    };
    response: ScaleOutClusterResponse;
  };
  /**根据集群ID修改该集群配置。 */
  ModifyCluster:{
    request:{
      params:{
        /**集群ID。 
 */
        ClusterId: string;
      };
      body: ModifyClusterBody;
    };
    response: ModifyClusterResponse;
  };
  /**调用MigrateCluster将集群从标准托管版迁移至Pro托管版。 */
  MigrateCluster:{
    request:{
      params:{
        /**集群ID。 */
        cluster_id: string;
      };
      body: MigrateClusterBody;
    };
    response: MigrateClusterResponse;
  };
  /**扩容Kubernetes集群 */
  ScaleCluster:{
    request:{
      params:{
        /**undefined */
        ClusterId: string;
      };
      body: ScaleClusterBody;
    };
    response: ScaleClusterResponse;
  };
  /**调用UpdateK8sClusterUserConfigExpire更新用户证书过期时间。 */
  UpdateK8sClusterUserConfigExpire:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: UpdateK8sClusterUserConfigExpireBody;
    };
    response: unknown;
  };
  /**根据集群ID查询该集群的所有资源。 */
  DescribeClusterResources:{
    request:{
      params:{
        /**集群ID。 
关于如何查看集群ID，请参见[查看集群信息](~~89446~~)。

您也可通过API接口[DescribeClustersV1](~~183905~~)，查看阿里云账号下所有创建的集群信息。 */
        ClusterId: string;
      };
    };
    response: DescribeClusterResourcesResponse[];
  };
  /**根据集群ID查询该集群的详情。 */
  DescribeClusterDetail:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
    };
    response: DescribeClusterDetailResponse;
  };
  /**查询集群及节点配额。 */
  DescribeUserQuota:{
    request:{
    };
    response: DescribeUserQuotaResponse;
  };
  /**查询已创建的所有ACK集群的详情。 */
  DescribeClustersV1:{
    request:{
      params:{
        /**集群名称。 
命名规则：由数字、汉字、英文字符或短划线（-）组成，长度范围1~63个字符，且不能以短划线（-）开头。 */
        name?: string;
        /**集群类型，取值： - `Kubernetes`: 专有版集群。
- `ManagedKubernetes`：包括托管版集群、ASK集群，边缘集群。
- `Ask`：ASK集群。
- `ExternalKubernetes`：注册集群。

其中查询ASK集群时集群类型参数的取值，取决于创建ASK集群时指定的值。
 */
        cluster_type?: string;
        /**每页显示的记录数。 */
        page_size?: number;
        /**当前页码。 */
        page_number?: number;
        /**集群标识，当集群类型选择`ManagedKubernetes`时，通过集群标识区分集群类型，取值： - `Default`：托管版集群。
- `Serverless`：ASK集群。
- `Edge`：边缘集群。

默认值为空值，取值可以为空，为空时不通过该字段进行过滤。 */
        profile?: string;
        /**集群规格，当集群类型选择`ManagedKubernetes`时，通过集群规格区分集群的类型，取值： - `ack.pro.small`：专业托管集群，即ACK Pro版集群。
- `ack.standard`：标准托管集群。

默认值为空值，取值可以为空，为空时不通过该字段进行过滤。 */
        cluster_spec?: string;
        /**集群地域。通过指定该字段，可以过滤出该地域下的集群列表。 */
        region_id?: string;
      };
    };
    response: DescribeClustersV1Response;
  };
  /**根据集群ID查询集群中部署注册集群的代理配置。 */
  DescribeExternalAgent:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**是否获取内网访问凭据。 
- `true`：仅获取内网连接凭据。
- `false`：仅获取公网连接凭据。

默认值：`false`。 */
        PrivateIpAddress?: string;
      };
    };
    response: DescribeExternalAgentResponse;
  };
  /**根据集群ID，查询集群日志。 */
  DescribeClusterLogs:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
    };
    response: DescribeClusterLogsResponse[];
  };
  /**根据任务ID，查询该任务执行详情。 */
  DescribeTaskInfo:{
    request:{
      params:{
        /**任务ID。 */
        task_id: string;
      };
    };
    response: DescribeTaskInfoResponse;
  };
  /**查询ACK支持的Kubernetes版本详情。 */
  DescribeKubernetesVersionMetadata:{
    request:{
      params:{
        /**集群所在地域ID。 */
        Region: string;
        /**集群类型，取值： 
- `Kubernetes`: 专有版集群。
- `ManagedKubernetes`：托管版集群。
- `ExternalKubernetes`：注册集群。
 */
        ClusterType: string;
        /**集群版本，与Kubernetes社区基线版本保持一致。建议选择最新版本，若不指定，默认使用最新版本。 
目前在ACK控制台您可以创建两种最新版本的集群。您可以通过API创建其他Kubernetes版本集群。ACK支持的Kubernetes版本，请参见[Kubernetes版本发布概览](~~185269~~)。 */
        KubernetesVersion?: string;
        /**面向场景时的集群类型，取值： 
- `Default`：非边缘场景集群。
- `Edge`：边缘场景集群。
- `Serverless`：ASK集群。

默认值：`Default`。 */
        Profile?: string;
        /**运行时类型，可以通过指定运行时类型，过滤出运行时所支持的系统镜像，取值： 
- `docker`：docker运行时。
- `containerd`：containerd运行时。
- `Sandboxed-Container.runv`：安全沙箱。

若指定运行时类型，将返回指定运行时支持的镜像版本。

若不指定运行时类型，默认返回全部镜像。 */
        runtime?: string;
        /**查询模式，取值： - `supported`：查询受支持版本
- `creatable`：查询可创建版本

若指定`KubernetesVersion`，将忽略查询模式。

若不指定查询模式，默认返回可创建版本。 */
        Mode?: string;
      };
    };
    response: DescribeKubernetesVersionMetadataResponse[];
  };
  /**根据集群ID查询访问该集群的kubeconfig配置。 */
  DescribeClusterUserKubeconfig:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**是否获取内网连接配置。取值： - `true`：仅获取内网连接凭据。
- `false`：仅获取公网连接凭据。 

默认值：`false`。 */
        PrivateIpAddress?: boolean;
        /**临时kubeconfig有效期，单位：分钟。取值范围：15（15分钟）～4320（3天）。 >当不设置该参数时，将由系统自动确定一个更长的有效期，具体过期时间通过返回的`expiration`字段的值确定。 */
        TemporaryDurationMinutes?: number;
      };
    };
    response: DescribeClusterUserKubeconfigResponse;
  };
  /**调用DescribeClusterAddonUpgradeStatus查询集群组件升级状态。 */
  DescribeClusterAddonUpgradeStatus:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**组件ID，例如：nginx-ingress-controller、flexvolume、metrics-server。 
集群支持的组件，可通过API `DescribeAddons` 进行查询。 */
        ComponentId: string;
      };
    };
    response: any;
  };
  /**查看容器服务中创建的所有集群（包括Swarm和Kubernetes集群）。 */
  DescribeClusters:{
    request:{
      params:{
        /**根据集群Name进行模糊匹配查询。 */
        name?: string;
        /**集群类型。 */
        clusterType?: string;
      };
    };
    response: DescribeClustersResponse[];
  };
  /**调用CreateAutoscalingConfig创建自动伸缩配置。 */
  CreateAutoscalingConfig:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: CreateAutoscalingConfigBody;
    };
    response: unknown;
  };
  /**获取集群kubeconfig接口 */
  DescribeClusterV2UserKubeconfig:{
    request:{
      params:{
        /**undefined */
        ClusterId: string;
        /**undefined */
        PrivateIpAddress?: boolean;
      };
    };
    response: DescribeClusterV2UserKubeconfigResponse;
  };
  /**根据节点名称，移除集群中指定节点。 */
  DeleteClusterNodes:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: DeleteClusterNodesBody;
    };
    response: DeleteClusterNodesResponse;
  };
  /**移除指定集群额外节点。 */
  RemoveClusterNodes:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: RemoveClusterNodesBody;
    };
    response: unknown;
  };
  /**添加已有ECS实例到ACK集群。 */
  AttachInstances:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: AttachInstancesBody;
    };
    response: AttachInstancesResponse;
  };
  /**查询手动添加实例到集群的脚本。 */
  DescribeClusterAttachScripts:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: DescribeClusterAttachScriptsBody;
    };
    response: string;
  };
  /**根据集群ID，查询该集群中的所有节点的详情。 */
  DescribeClusterNodes:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**节点实例ID列表，多值使用英文逗号（,）分隔。 */
        instanceIds?: string;
        /**节点池ID。 */
        nodepool_id?: string;
        /**集群节点状态，按照集群节点运行状态进行过滤，取值： 
- `all`：以下4种状态的集群节点。
- `running`：正在运行的集群节点。
- `removing`：正在删除的集群节点。
- `initial`：正在初始化的集群节点。
- `failed`：创建失败的集群节点。

默认值：`all`。 */
        state?: string;
        /**每页显示的记录数。取值范围为[1,100]。 
默认值：10。 */
        pageSize?: string;
        /**页码。 
默认值：1。 */
        pageNumber?: string;
      };
    };
    response: DescribeClusterNodesResponse;
  };
  /**调用CreateClusterNodePool为集群创建节点池。 */
  CreateClusterNodePool:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: CreateClusterNodePoolBody;
    };
    response: CreateClusterNodePoolResponse;
  };
  /**根据节点池ID，删除集群节点池。 */
  DeleteClusterNodepool:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**节点池ID。 */
        NodepoolId: string;
        /**是否强制删除。 */
        force?: boolean;
      };
    };
    response: DeleteClusterNodepoolResponse;
  };
  /**根据节点池ID，扩容节点池节点。 */
  ScaleClusterNodePool:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**节点池ID。 */
        NodepoolId: string;
      };
      body: ScaleClusterNodePoolBody;
    };
    response: ScaleClusterNodePoolResponse;
  };
  /**调用ModifyClusterNodePool更新节点配置。 */
  ModifyClusterNodePool:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**节点池ID。 */
        NodepoolId: string;
      };
      body: ModifyClusterNodePoolBody;
    };
    response: ModifyClusterNodePoolResponse;
  };
  /**调用DescribeClusterNodePools查询集群内所有节点池详情。 */
  DescribeClusterNodePools:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
    };
    response: DescribeClusterNodePoolsResponse;
  };
  /**根据节点池ID，查询集群中该节点池的详情。 */
  DescribeClusterNodePoolDetail:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**节点池ID。 */
        NodepoolId: string;
      };
    };
    response: DescribeClusterNodePoolDetailResponse;
  };
  /**调用RepairClusterNodePool修复指定的托管节点池内指定节点存在的问题。 */
  RepairClusterNodePool:{
    request:{
      params:{
        /**集群ID */
        cluster_id: string;
        /**节点池ID */
        nodepool_id: string;
      };
      body: RepairClusterNodePoolBody;
    };
    response: RepairClusterNodePoolResponse;
  };
  /**升级指定集群节点池的Kubernetes版本、操作系统版本或容器运行时版本。 */
  UpgradeClusterNodepool:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**节点池ID。 */
        NodepoolId: string;
      };
      body: UpgradeClusterNodepoolBody;
    };
    response: UpgradeClusterNodepoolResponse;
  };
  /**取消处于升级状态的ACK集群。 */
  CancelClusterUpgrade:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
    };
    response: unknown;
  };
  /**根据集群ID，恢复升级处于升级暂停状态的集群。 */
  ResumeUpgradeCluster:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
    };
    response: unknown;
  };
  /**根据集群ID，升级指定集群。 */
  UpgradeCluster:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: UpgradeClusterBody;
    };
    response: unknown;
  };
  /**暂停用户集群升级。 */
  PauseClusterUpgrade:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
    };
    response: unknown;
  };
  /**根据集群ID，查询该集群的升级状态。 */
  GetUpgradeStatus:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
    };
    response: GetUpgradeStatusResponse;
  };
  /**激活云原生一体机接口。 */
  CreateEdgeMachine:{
    request:{
      body: CreateEdgeMachineBody;
    };
    response: CreateEdgeMachineResponse;
  };
  /**删除云原生一体机。 */
  DeleteEdgeMachine:{
    request:{
      params:{
        /**云原生一体机ID */
        edge_machineid: string;
        /**是否强制删除云原生一体机，取值： 
- `true`：表示强制删除云原生一体机。

- `false`：表示不强制删除云原生一体机。

默认值：`false`。 */
        force?: string;
      };
    };
    response: unknown;
  };
  /**调用DescribeEdgeMachineActiveProcess查询云原生一体机激活进度。 */
  DescribeEdgeMachineActiveProcess:{
    request:{
      params:{
        /**云原生一体机的ID */
        edge_machineid: string;
      };
    };
    response: DescribeEdgeMachineActiveProcessResponse;
  };
  /**查询云原生一体机型号。 */
  DescribeEdgeMachineModels:{
    request:{
    };
    response: DescribeEdgeMachineModelsResponse;
  };
  /**查询云原生一体机列表。 */
  DescribeEdgeMachines:{
    request:{
      params:{
        /**云原生一体机`hostname` */
        hostname?: string;
        /**云原生一体机类型 */
        model?: string;
        /**云原生一体机的在线状态，取值： 
- `offline`：离线

- `online`：在线 */
        online_state?: string;
        /**生命周期状态 */
        life_state?: string;
        /**每页显示的记录数 */
        page_size?: number;
        /**当前页码 */
        page_number?: number;
      };
    };
    response: DescribeEdgeMachinesResponse;
  };
  /**获取云原生一体机SSH Token。 */
  DescribeEdgeMachineTunnelConfigDetail:{
    request:{
      params:{
        /**云原生一体机ID */
        edge_machineid: string;
      };
    };
    response: DescribeEdgeMachineTunnelConfigDetailResponse;
  };
  /**在ACK@Edge集群中添加一体机。 */
  EdgeClusterAddEdgeMachine:{
    request:{
      params:{
        /**集群ID。 */
        clusterid: string;
        /**云原生一体机ID。 */
        edge_machineid: string;
      };
      body: EdgeClusterAddEdgeMachineBody;
    };
    response: EdgeClusterAddEdgeMachineResponse;
  };
  /**创建一个编排模板。 */
  CreateTemplate:{
    request:{
      body: CreateTemplateBody;
    };
    response: CreateTemplateResponse;
  };
  /**根据编排模板ID，删除指定编排模板。 */
  DeleteTemplate:{
    request:{
      params:{
        /**编排模板ID。 */
        TemplateId: string;
      };
    };
    response: unknown;
  };
  /**根据编排模板ID，更新编排模板。 */
  UpdateTemplate:{
    request:{
      params:{
        /**模板ID。 */
        TemplateId: string;
      };
      body: UpdateTemplateBody;
    };
    response: unknown;
  };
  /**查询已创建的所有编排模板详情。 */
  DescribeTemplates:{
    request:{
      params:{
        /**模板类型，值可以是任意值。 
- 当取值是`kubernetes`时将在控制台的编排模板页面展示该模板。

- 当取值是`compose`时将不在控制台的编排模板页面展示该模板。

默认值：`kubernetes`。 */
        template_type?: string;
        /**对查询结果进行分页处理，指定返回第几页的数据。 
默认值： 1。 */
        page_num?: number;
        /**对查询结果进行分页处理，指定每页包含的数据条数。 
默认值：10。 */
        page_size?: number;
      };
    };
    response: DescribeTemplatesResponse;
  };
  /**根据编排模板ID，查询该编排模板的详情。 */
  DescribeTemplateAttribute:{
    request:{
      params:{
        /**模板ID。 */
        TemplateId: string;
        /**模板类型，其值可以是任意值。 
- 当取值是`kubernetes`时将在控制台的编排模板页面展示该模板。

- 当取值是`compose`时将在控制台Swarm集群页面显示该模板（已废弃）。

- 当取值非`kubernetes`时在控制台的编排模板页面将不会展示该模板，推荐使用`kubernetes`。

默认值：`kubernetes`。
 */
        template_type?: string;
      };
    };
    response: DescribeTemplateAttributeResponse[];
  };
  /**为应用创建触发器。 */
  CreateTrigger:{
    request:{
      params:{
        /**集群ID。 */
        cluster_id: string;
      };
      body: CreateTriggerBody;
    };
    response: CreateTriggerResponse;
  };
  /**删除应用触发器。 */
  DeleteTrigger:{
    request:{
      params:{
        /**集群ID。 */
        cluster_id: string;
        /**触发器ID。 */
        Id: string;
      };
    };
    response: unknown;
  };
  /**查询指定的触发器。 */
  DescribeTrigger:{
    request:{
      params:{
        /**集群ID。 */
        cluster_id: string;
        /**应用所属命名空间。 */
        Namespace: string;
        /**触发器类型。取值： 
- `deployment`：针对无状态应用的触发器。 

- `application`：针对应用中心应用的触发器。

默认值：`deployment`。

当不指定触发器类型时，查询结果将不过滤触发器类型。 */
        Type?: string;
        /**应用名称。 */
        Name: string;
        /**触发器行为，取值： 
`redeploy`：重新部署`project_id`中定义的资源。

当不指定触发器行为时，查询结果将不过滤触发器行为。 */
        action?: string;
      };
    };
    response: DescribeTriggerResponse[];
  };
  /**为应用创建触发器。 */
  CreateKubernetesTrigger:{
    request:{
      body: CreateKubernetesTriggerBody;
    };
    response: CreateKubernetesTriggerResponse;
  };
  /**根据应用触发器ID，删除应用触发器。 */
  DeleteKubernetesTrigger:{
    request:{
      params:{
        /**触发器ID。 */
        Id: string;
      };
    };
    response: unknown;
  };
  /**根据应用名称查询该应用的触发器。 */
  GetKubernetesTrigger:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**命名空间名称。 */
        Namespace: string;
        /**触发器类型。取值： 
- `deployment`：针对无状态应用的触发器。 

- `application`：针对应用中心应用的触发器。

默认值：`deployment`。

当不指定触发器类型时，查询结果将不过滤触发器类型。 */
        Type?: string;
        /**应用名称。 */
        Name: string;
        /**触发器行为，取值： 
`redeploy`：重新部署`project_id`中定义的资源。

当不指定触发器行为时，查询结果将不过滤触发器行为。 */
        action?: string;
      };
    };
    response: GetKubernetesTriggerResponse[];
  };
  /**为集群安装组件。 */
  InstallClusterAddons:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: InstallClusterAddonsBody[];
    };
    response: unknown;
  };
  /**根据组件名称，卸载指定集群的组件。 */
  UnInstallClusterAddons:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: UnInstallClusterAddonsBody[];
    };
    response: unknown;
  };
  /**调用DescribeClusterAddonMetadata查询特定集群可使用的指定组件版本信息，包括组件版本、可配置参数等。 */
  DescribeClusterAddonMetadata:{
    request:{
      params:{
        /**集群ID */
        cluster_id: string;
        /**组件ID */
        component_id: string;
        /**组件版本 */
        version?: string;
      };
    };
    response: DescribeClusterAddonMetadataResponse;
  };
  /**调用ModifyClusterAddon修改已安装的集群组件配置。修改配置可能会导致组件重新部署和启动，请评估影响后再进行操作。 */
  ModifyClusterAddon:{
    request:{
      params:{
        /**集群ID。 */
        cluster_id: string;
        /**组件ID。 */
        component_id: string;
      };
      body: ModifyClusterAddonBody;
    };
    response: unknown;
  };
  /**根据组件名称，将指定组件升级到指定版本。 */
  UpgradeClusterAddons:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: UpgradeClusterAddonsBody[];
    };
    response: unknown;
  };
  /**调用PauseComponentUpgrade暂停组件升级。 */
  PauseComponentUpgrade:{
    request:{
      params:{
        /**集群ID。 */
        clusterid: string;
        /**组件ID。 */
        componentid: string;
      };
    };
    response: unknown;
  };
  /**调用ResumeComponentUpgrade重新启动被暂停的组件升级任务。 */
  ResumeComponentUpgrade:{
    request:{
      params:{
        /**集群ID。 */
        clusterid: string;
        /**组件ID。 */
        componentid: string;
      };
    };
    response: unknown;
  };
  /**取消集群组件升级。 */
  CancelComponentUpgrade:{
    request:{
      params:{
        /**集群ID。 */
        clusterId: string;
        /**组件ID。 */
        componentId: string;
      };
    };
    response: unknown;
  };
  /**查询平台支持的所有组件的详情。 */
  DescribeAddons:{
    request:{
      params:{
        /**集群所在地域ID。 */
        region: string;
        /**集群类型，取值： 
- `Kubernetes`: 专有版集群。
- `ManagedKubernetes`：托管版集群。
- `Ask`：ASK集群。
- `ExternalKubernetes`：注册集群。 */
        cluster_type?: string;
        /**集群类型，取值： - `Default`:托管版集群。
- `Serverless`：Serverless集群。
- `Edge`：边缘集群。 */
        cluster_profile?: string;
        /**集群规格，当集群类型选择`ManagedKubernetes`时，通过集群规格区分集群的类型，取值： - `ack.pro.small`：专业托管集群，即ACK Pro版集群。
- `ack.standard`：标准托管集群。

默认值为空值，取值可以为空，为空时不通过该字段进行过滤。 */
        cluster_spec?: string;
        /**集群版本。 */
        cluster_version?: string;
      };
    };
    response: DescribeAddonsResponse;
  };
  /**根据组件名称查询该组件升级状态。 */
  DescribeClusterAddonsUpgradeStatus:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**组件名称列表。 */
        componentIds: string[];
      };
    };
    response: any;
  };
  /**根据集群ID，查询集群中已安装的所有组件的详情。 */
  DescribeClusterAddonsVersion:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
    };
    response: any;
  };
  /**调用UntagResources为指定的集群解绑标签。 */
  UntagResources:{
    request:{
      params:{
        /**资源所属的地域ID。 */
        region_id: string;
        /**资源ID列表。 */
        resource_ids: string[];
        /**资源类型定义。取值范围：只支持`CLUSTER`这一种资源类型。 */
        resource_type: string;
        /**资源标签键列表。 */
        tag_keys: string[];
        /**是否删除全部自定义标签，仅当`tag_keys`为空时生效。取值范围： 
- `true`: 删除全部标签。
- `false`: 不删除全部标签。 */
        all?: boolean;
      };
    };
    response: UntagResourcesResponse;
  };
  /**调用TagResources为指定的集群绑定特定标签。 */
  TagResources:{
    request:{
      body: TagResourcesBody;
    };
    response: TagResourcesResponse;
  };
  /**根据集群ID修改该集群的集群标签。 */
  ModifyClusterTags:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: Tag[];
    };
    response: unknown;
  };
  /**根据集群ID，查询指定集群资源的标签。 */
  ListTagResources:{
    request:{
      params:{
        /**要查询的集群ID列表。 
 */
        resource_ids: string[];
        /**资源类型，目前支持：`CLUSTER`。 */
        resource_type: string;
        /**地域ID。 */
        region_id: string;
        /**要查询的标签列表，限制最多包含20个子项。 
 */
        tags?: Tag[];
        /**下一个查询开始的令牌。 */
        next_token?: string;
      };
    };
    response: ListTagResourcesResponse;
  };
  /**只针对托管版集群。 */
  ModifyClusterConfiguration:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: ModifyClusterConfigurationBody;
    };
    response: unknown;
  };
  /**调用DeletePolicyInstance在指定集群中删除策略规则实例。 */
  DeletePolicyInstance:{
    request:{
      params:{
        /**目标集群ID。 */
        cluster_id: string;
        /**策略治理规则名称。 */
        policy_name: string;
        /**策略规则实例ID。 */
        instance_name?: string;
      };
    };
    response: DeletePolicyInstanceResponse;
  };
  /**调用ModifyPolicyInstance在指定集群中更新策略规则实例。 */
  ModifyPolicyInstance:{
    request:{
      params:{
        /**目标集群ID */
        cluster_id: string;
        /**策略治理规则名称 */
        policy_name: string;
      };
      body: ModifyPolicyInstanceBody;
    };
    response: ModifyPolicyInstanceResponse;
  };
  /**调用DescribePolicies列举策略治理规则库列表。 */
  DescribePolicies:{
    request:{
    };
    response: any;
  };
  /**获取策略规则模板详情。 */
  DescribePolicyDetails:{
    request:{
      params:{
        /**策略治理规则名称 */
        policy_name: string;
      };
    };
    response: DescribePolicyDetailsResponse;
  };
  /**调用DescribePolicyGovernanceInCluster获取集群策略治理详情。 */
  DescribePolicyGovernanceInCluster:{
    request:{
      params:{
        /**目标集群ID */
        cluster_id: string;
      };
    };
    response: DescribePolicyGovernanceInClusterResponse;
  };
  /**调用DescribePolicyInstances获取集群中当前部署的策略实例。 */
  DescribePolicyInstances:{
    request:{
      params:{
        /**目标集群ID */
        cluster_id: string;
        /**策略治理规则名称 */
        policy_name?: string;
        /**策略实例名称 */
        instance_name?: string;
      };
    };
    response: DescribePolicyInstancesResponse[];
  };
  /**获取集群当前不同策略类型对应的实例部署状态，包括每种策略规则对应开启的实例计数，以及不同治理等级下开启的策略种类计数。 */
  DescribePolicyInstancesStatus:{
    request:{
      params:{
        /**目标集群ID */
        cluster_id: string;
      };
    };
    response: DescribePolicyInstancesStatusResponse;
  };
  /**调用DeployPolicyInstance在指定集群中部署策略规则实例。 */
  DeployPolicyInstance:{
    request:{
      params:{
        /**目标集群ID。 */
        cluster_id: string;
        /**策略治理规则名称。 */
        policy_name: string;
      };
      body: DeployPolicyInstanceBody;
    };
    response: DeployPolicyInstanceResponse;
  };
  /**自动修复指定集群节点池的漏洞。 */
  FixNodePoolVuls:{
    request:{
      params:{
        /**集群ID。 */
        cluster_id: string;
        /**节点池ID。 */
        nodepool_id: string;
      };
      body: FixNodePoolVulsBody;
    };
    response: FixNodePoolVulsResponse;
  };
  /**查询指定集群节点池的漏洞列表。 */
  DescribeNodePoolVuls:{
    request:{
      params:{
        /**集群ID。 */
        cluster_id: string;
        /**节点池ID。 */
        nodepool_id: string;
        /**要查询的漏洞修复必要性等级。多个等级之间使用半角逗号（,）分隔。取值： 
- `asap`：高
- `later`：中
- `nntf`：低 */
        necessity?: string;
      };
    };
    response: DescribeNodePoolVulsResponse;
  };
  /**暂停执行中的集群任务。 */
  PauseTask:{
    request:{
      params:{
        /**任务ID。 */
        task_id: string;
      };
    };
    response: unknown;
  };
  /**恢复已暂停的集群任务。 */
  ResumeTask:{
    request:{
      params:{
        /**任务ID。 */
        task_id: string;
      };
    };
    response: unknown;
  };
  /**取消集群任务执行。 */
  CancelTask:{
    request:{
      params:{
        /**任务ID。 */
        task_id: string;
      };
    };
    response: unknown;
  };
  /**查询指定集群的事件列表。 */
  DescribeClusterEvents:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**每页大小，取值范围1-50。 默认值：50。 */
        page_size?: number;
        /**分页页数。 */
        page_number?: number;
        /**查询的任务ID。 */
        task_id?: string;
      };
    };
    response: DescribeClusterEventsResponse;
  };
  /**根据集群ID查询集群任务列表。 */
  DescribeClusterTasks:{
    request:{
      params:{
        /**集群id。 */
        cluster_id?: string;
      };
    };
    response: DescribeClusterTasksResponse;
  };
  /**修改节点池节点配置信息。 */
  ModifyNodePoolNodeConfig:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**节点池ID。 */
        NodepoolId: string;
      };
      body: ModifyNodePoolNodeConfigBody;
    };
    response: ModifyNodePoolNodeConfigResponse;
  };
  /**移除节点池内的节点 */
  RemoveNodePoolNodes:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**节点池ID。 */
        NodepoolId: string;
        /**是否释放节点。可选值： - true：释放。
- false：不释放。 */
        release_node?: boolean;
        /**是否排水节点。可选值： - true：排水。
- false：不排水。 */
        drain_node?: boolean;
        /**移除节点列表。 */
        nodes?: string[];
        /**移除实例列表。 */
        instance_ids?: string[];
      };
    };
    response: RemoveNodePoolNodesResponse;
  };
  /**同步节点池，包含节点池元数据、节点池内节点信息等。 */
  SyncClusterNodePool:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
    };
    response: SyncClusterNodePoolResponse;
  };
  /**使用主账号调用DescribeSubaccountK8sClusterUserConfig，获取包含账号内指定RAM用户或角色身份信息的Kubernetes集群的KubeConfig凭证。 */
  DescribeSubaccountK8sClusterUserConfig:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**阿里云账号内指定的RAM用户或RAM角色ID。 */
        Uid: string;
        /**是否获取内网连接配置。取值： - `true`：仅获取内网连接的KubeConfig凭证。
- `false`：仅获取公网连接的KubeConfig凭证。

默认值：`false`。 */
        PrivateIpAddress?: boolean;
        /**临时KubeConfig有效期，单位：分钟。 
取值范围：15分钟～4320分钟（3天）。
> 当不设置该参数时，将由系统自动确定一个更长的有效期，具体过期时间通过返回的expiration字段的值确定。 */
        TemporaryDurationMinutes?: number;
      };
    };
    response: DescribeSubaccountK8sClusterUserConfigResponse;
  };
  /**扫描集群安全漏洞。 */
  ScanClusterVuls:{
    request:{
      params:{
        /**集群ID。 */
        cluster_id?: string;
      };
    };
    response: ScanClusterVulsResponse;
  };
  /**查询集群安全漏洞信息。 */
  DescribeClusterVuls:{
    request:{
      params:{
        /**集群ID。 */
        cluster_id?: string;
      };
    };
    response: DescribeClusterVulsResponse;
  };
  /**添加已有节点到节点池。 */
  AttachInstancesToNodePool:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
        /**节点池ID。 */
        NodepoolId: string;
      };
      body: AttachInstancesToNodePoolBody;
    };
    response: AttachInstancesToNodePoolResponse;
  };
  /**调用DescribeClusterAddonInstance查询已安装的集群组件的版本、状态和配置等信息。 */
  DescribeClusterAddonInstance:{
    request:{
      params:{
        /**集群ID。 */
        ClusterID?: string;
        /**组件名称。 */
        AddonName?: string;
      };
    };
    response: DescribeClusterAddonInstanceResponse;
  };
  /**调用CheckControlPlaneLogEnable查询ACK托管集群控制平面组件日志当前配置状态。 */
  CheckControlPlaneLogEnable:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
    };
    response: CheckControlPlaneLogEnableResponse;
  };
  /**调用UpdateControlPlaneLog修改ACK托管集群控制平面组件日志配置。 */
  UpdateControlPlaneLog:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
      body: UpdateControlPlaneLogBody;
    };
    response: unknown;
  };
  /**列举用户集群命名空间 */
  DescribeUserClusterNamespaces:{
    request:{
      params:{
        /**集群ID。 */
        ClusterId: string;
      };
    };
    response: string[];
  };
};