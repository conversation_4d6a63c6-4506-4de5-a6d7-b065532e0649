import { ApiType } from "@alicloud/xconsole-service";
import { AxiosRequestConfig } from "axios";

/**createService interfaces */
export interface IOptionsData {
  product?: string;
  action?: string;
  actions?: string | Actions;
  [key: string]: any;
}
export type Action = {
  action: string;
  params?: any;
  customRequestKey?: string;
  version?: string;
  product?: string;
  data?: any;
};
export type Actions = Action[];
export type Risk = {
  code?: {
    [key: string]: string;
  };
  url?: {
    [key: string]: string;
  };
};
export interface IOptions extends AxiosRequestConfig {
  apiType?: ApiType;
  fastLogin?: boolean;
  ignoreError?: boolean;
  description?: any;
  useCors?: boolean;
  data?: IOptionsData;
  risk?: Risk;
  url?: string;
  baseURL?: string;
  // method?: AxiosRequestConfig['method'];
  requestStartTime?: number;
  originParams?: any;
  originData?: any;
  mock?: boolean;
  region?: string;
  /**
   * 为true时返回response.data,为false时返回response.data.data
   */
  rawResponseData?: boolean;
  extraData?: any;
  /**
   * 在请求失败的时候不 throw Error 而是直接返回请求对象response.data
   */
  disableThrowResponseError?: boolean;
  throwDoubleConfirmError?: boolean;
}
