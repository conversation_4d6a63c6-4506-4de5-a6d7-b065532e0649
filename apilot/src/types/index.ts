import { IOptions, Actions } from "./createServiceInterfaces";
import { AxiosInstance } from "axios";

export interface APIOptions extends IOptions {
  /**自定义错误处理 */
  // customErrorHandler?: Function;
  /**自定义请求 */
  createInstance?: () => AxiosInstance;
  /**product code */
  productCode?: string;
  /**请求类型 */
  type?: "pop" | "custom" | "atlas" | "mock";
  /**是否新版风控 */
  isRisk?: boolean;
  /**fecs请求是否自动合并 */
  fecsConfig?: {
    autoMulti?: boolean;
  };
  atlasConfig?: {
    /**本地调试端口号 */
    port?: string;
    /**调试模式下调用本地接口 */
    debugMode?: boolean;
  };
}

export type Action<PCode> = keyof PCode | "";

// 新增：支持 MultiAPI 的联合类型
export type ActionOrActions<PCode> = keyof PCode | "" | Actions;

// export type RequestParams<T,K> = T[K]['request'] | {};
//@ts-ignore
export type RequestParams<Act> = Act["request"] | {};

export type OneConsoleResponse<T> = {
  data: T;
  requestId: string;
  successResponse: boolean;
  code: string;
};

export type ResponseType<Act> = Promise<
  //@ts-ignore
  // OneConsoleResponse<Act["response"]>
  Act["response"]
>;

// export interface APIInstance {
//   // <PCode, Act>(
//   //   action: Action<PCode>,
//   //   requestParams: RequestParams<Act>
//   // ): ResponseType<Act>;
//   create?: (config?: APIOptions) => APIInstance;
//   // createWithProduct: (productCode: string) => APIInstance;
// }

export interface APIInstance<PCode = any> {
  <Act extends keyof PCode, Res = PCode[Act]>(
    action: ActionOrActions<PCode>,
    requestParams?: RequestParams<PCode[Act]>
  ): ResponseType<Res>;
  create: <PCode = any>(config?: APIOptions) => APIInstance<PCode>;
  config: APIOptions;
}

declare global {
  interface Window {
    ALIYUN_CONSOLE_CONFIG: any;
  }
}
