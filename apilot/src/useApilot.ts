import useSwr, { SWRConfiguration } from "swr";
import { apilot as factory } from ".";
import {
  APIInstance,
  APIOptions,
  Action,
  OneConsoleResponse,
  RequestParams,
} from "./types";
import _ from "lodash";

export function useApilot<PCode = any>(
  productCode: string,
  config?: {
    /**自定义请求方法 */
    apilot?: APIInstance<PCode>;
    /**自定义请求配置，和fetcher之需要使用一个 */
    // apilotConfig?: APIOptions;
    swrConfig?: SWRConfiguration;
  }
) {
  // config = _.merge(config, { fetcherConfig: { productCode } });

  return <Act extends keyof PCode>(
    action: Act | "",
    requestParams?: RequestParams<PCode[Act]>
  ) => {
    let fetcher = config?.apilot;
    // if (!fetcher) fetcher = factory.create<PCode>(config?.apilotConfig);
    if (fetcher) fetcher = fetcher.create<PCode>({ productCode });
    requestParams = requestParams || {};
    //@ts-ignore
    const res = useSwr<PCode[Act]["response"]>(
      fetcher ? requestParams : null,
      () => fetcher?.(action, requestParams),
      config?.swrConfig
    );
    return res;
  };
}
