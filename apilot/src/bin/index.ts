#!/usr/bin/env ts-node
import { program } from "commander";
import { apiToInterface, getAPISchema } from "./getModels";
import { saveFile } from "./util";
const path = require("path");

program
  .command("init")
  .option("-pc,--productCode <string>", "项目代码")
  .option("-v,--version <string>", "版本号")
  .action((name, options, command) => {
    const { productCode, version } = name;
    const config = `export default {
      productCode:'${productCode}',
      version:'${version}'
    }`;
    getAPISchema(productCode, version).then((doc) => {
      const { interfaces, json } = apiToInterface(doc, productCode);
      // 保存json
      // saveFile(
      //   json,
      //   path.join(__dirname, `json/${productCode}_${version}-json.ts`)
      // );
      // saveFile(
      //   `export {default as ${productCode}Json} from './${productCode}_${version}-json';`,
      //   path.join(__dirname, "index.ts"),
      //   true
      // );
      // 保存命令配置
      // saveFile(config, path.join(__dirname, 'config.ts'));
      // 保存类型
      saveFile(
        interfaces,
        path.join(__dirname, `types/${productCode}_${version}.d.ts`)
      );
      saveFile(
        `export { default as ${productCode} } from "./${productCode}_${version}";`,
        path.join(__dirname, "types/index.d.ts"),
        true
      );
      saveFile(
        `export * from './${productCode}_${version}';`,
        path.join(__dirname, "types/index.d.ts"),
        true
      );
    });
  });

program.parse();
