//@ts-nocheck
// import json from './json';
// import axios from "axios";
import { reNameInterface } from "./util";
// import apiConfig from "./config";

const axios = require("axios");
let globalInterface = "";
let indentLevel = 0;
const unknownType = "unknown";
const interfaceField = "export interface";
const defaultInterfaceField = "export default interface";
// const {productCode,version}=apiConfig

export async function getAPISchema(productName: string, version: string) {
  const url = `https://next.api.aliyun.com/meta/v1/products/${productName}/versions/${version}/api-docs.json`;
  // const url = "http://127.0.0.1:5500/atlasRes.json";
  const doc = (await axios({ url }))?.data || {};
  return doc;
}

/**
 * 添加缩进和换行
 * @param str
 * @param spaceNum 缩进数量
 * @param addSemicolon 是否添加分号，默认为true
 * @returns
 */
function setSpacesAndLine(
  str: string,
  spaceNum: number = 0,
  addSemicolon: boolean = true
) {
  //   const indent = '&nbsp;&nbsp;';
  const indent = "  ";
  let res = "\n";
  for (let i = 0; i < spaceNum; ++i) {
    res += indent;
  }
  res += str;
  if (addSemicolon) res += ";";
  return res;
}

/**
 * 设置是否required
 * @param name 字段名
 * @param required 默认false
 * @returns
 */
function _required(name: string, required: boolean) {
  return required ? name : name + "?";
}

function _setName(name: string, prop: any) {
  if (name.includes(".")) name = `"${name}"`;
  return _required(name, prop.required);
}

/**
 * 添加JSDoc
 * @param str 注释内容
 * @param spaceNum 注释缩进
 * @returns
 */
function addExplaination(str: string, spaceNum: number = 0) {
  if (str) str = str.replace("\n", " ");
  return setSpacesAndLine(`/**${str} */`, spaceNum, false);
}

/**
 * 添加新的全局接口
 * @param properties 接口字段对象
 * @param name 自定义全局接口名称，默认随机生成
 * @returns 接口名称
 */
function addNewInterface(properties: any, name?: string) {
  if (!properties) return "any";
  if (!name) name = getRandInterfaceName();
  name = reNameInterface(name);
  let model = `${interfaceField} ${name}{`;
  for (const [fieldName, prop] of Object.entries(properties)) {
    if (prop.description) model += addExplaination(prop.description, 1);
    model += setSpacesAndLine(
      `${_setName(fieldName, prop)}: ${_setInterface(
        prop,
        `${name}_${fieldName}`
      )}`,
      1
    );
  }
  model += setSpacesAndLine("}", 0, false);
  globalInterface += setSpacesAndLine(model, 0, false);
  return name;
}

/**
 * 设置字段对应的类型
 * @param field 字段对象
 * @returns 类型
 */
function _setInterface(field, fieldName?) {
  if (!field) return unknownType;
  if (field.$ref) {
    const commonInterfaceName = field.$ref.split("/").pop();
    return reNameInterface(commonInterfaceName);
  }
  if (field.type === "object") {
    return addNewInterface(field.properties, fieldName);
  }
  if (field.type === "array") {
    return _setInterface(field.items, fieldName) + "[]";
  }
  const typeMap = {
    string: "string",
    integer: "number",
    boolean: "boolean",
    number: "number",
  };
  return typeMap[field.type] || unknownType;
}

/**
 * 解析schema为interfaces
 * @param json
 */
export function apiToInterface(json: object, productName: string) {
  const { apis } = json;
  const commonSchema = json.components?.schemas;
  const toSaveJson = {};
  //添加全局interface
  let commonInterfaces = "";
  if (commonSchema) {
    for (const [fieldName, prop] of Object.entries(commonSchema)) {
      commonInterfaces += setSpacesAndLine(
        `export type ${reNameInterface(fieldName)} = ${_setInterface(prop)}`,
        undefined,
        false
      );
    }
  }
  globalInterface += commonInterfaces;
  let res = `${defaultInterfaceField} ${productName}{`;
  for (const [action, value] of Object.entries(apis)) {
    const { parameters } = value;
    indentLevel = 1;
    if (value.summary) res += addExplaination(value.summary, indentLevel);
    res += setSpacesAndLine(`${action}:{`, indentLevel++, false);
    //request
    res += setSpacesAndLine("request:{", indentLevel++, false);
    // request.params: query/path
    const params = parameters?.filter((param) => param.in !== "body");

    if (params && !!params.length) {
      res += setSpacesAndLine("params:{", indentLevel++, false);
      params.map((param) => {
        const { name, schema } = param;
        res += addExplaination(schema.description, indentLevel);
        res += setSpacesAndLine(
          `${_setName(name, schema)}: ${_setInterface(
            schema,
            `${action}_${name}`
          )}`,
          indentLevel
        );
      });
      res += setSpacesAndLine("}", --indentLevel);
    }

    //request.content: body
    const bodyRequest = parameters?.find((param) => param.in === "body");
    if (bodyRequest) {
      res += setSpacesAndLine("content: ", indentLevel, false);
      res += _setInterface(bodyRequest?.schema, `${action}Body`) + ";";
    }
    res += setSpacesAndLine("}", --indentLevel);

    //response
    res += setSpacesAndLine("response: ", indentLevel, false);
    const response = value.responses?.["200"]?.schema;
    res += _setInterface(response, `${action}Response`) + ";";
    res += setSpacesAndLine("}", --indentLevel);

    //保存简化版
    toSaveJson[action] = getSimplifiedJson(value);
  }
  // console.log('保存的json', toSaveJson);
  res += setSpacesAndLine("}", undefined, false);
  res = globalInterface + setSpacesAndLine(res, 0, false);
  // exportInterfaces(res);
  // saveSchema(JSON.stringify(toSaveJson));
  return {
    interfaces: res,
    json: `export default ${JSON.stringify(toSaveJson)}`,
    // exportDefault: `export { default as ${productName} } from "./${productName}";`, //保存到./types/index中
  };
}

/**
 * 生成随机Interface名字
 * @param length 名字长度
 * @returns
 */
function getRandInterfaceName(length = 8) {
  let res = "";
  const chars = "abcdefghijklmnopqrstuvwxyz";
  const len = chars.length;
  for (let i = 0; i < length; i++) {
    res += chars.charAt(Math.floor(Math.random() * len));
  }
  return res.toUpperCase();
}

/**
 * 每个action需保存的部分
 * @param prop
 * @returns
 */
function getSimplifiedJson(prop) {
  if (!prop) return {};
  return {
    path: prop.path,
    methods: prop.methods,
    parameters: prop.parameters
      ?.filter((param) => param.in !== "body")
      ?.map(({ name, in: loc }) => ({ name, loc })),
  };
}
