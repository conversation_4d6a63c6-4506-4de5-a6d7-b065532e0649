// import * as FileSaver from 'file-saver';
const fs = require('fs');

export function saveFile(content: string, fileName: string, isAppend?: boolean) {
  // const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
  // FileSaver.saveAs(blob, fileName);
  function handleError(err: string) {
    if (err) throw new Error('文件保存失败' + err);
    else console.log('保存成功', fileName);
  }
  if (isAppend) fs.appendFile(fileName, content, handleError);
  else fs.writeFile(fileName, content, handleError);
}

export function getFileContent(fileName: string) {
  fs.readFile(fileName);
}

export function reNameInterface(name: string) {
  return name
    .split('_')
    .map(item => item.charAt(0).toUpperCase() + item.slice(1))
    .join('');
}
