export default {"DescribeEvents":{"path":"/events","methods":["get"],"parameters":[{"name":"cluster_id","loc":"query"},{"name":"type","loc":"query"},{"name":"page_size","loc":"query"},{"name":"page_number","loc":"query"}]},"DeleteAlertContact":{"path":"/alert/contacts","methods":["delete"],"parameters":[]},"DeleteAlertContactGroup":{"path":"/alert/contact_groups","methods":["delete"],"parameters":[]},"StartAlert":{"path":"/alert/{ClusterId}/alert_rule/start","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"StopAlert":{"path":"/alert/{ClusterId}/alert_rule/stop","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"UpdateContactGroupForAlert":{"path":"/alert/{ClusterId}/alert_rule/contact_groups","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"OpenAckService":{"path":"/service/open","methods":["post"],"parameters":[{"name":"type","loc":"query"}]},"GrantPermissions":{"path":"/permissions/users/{uid}","methods":["post"],"parameters":[{"name":"uid","loc":"path"}]},"DescribeUserPermission":{"path":"/permissions/users/{uid}","methods":["get"],"parameters":[{"name":"uid","loc":"path"}]},"StartWorkflow":{"path":"/gs/workflow","methods":["post"],"parameters":[]},"RemoveWorkflow":{"path":"/gs/workflow/{workflowName}","methods":["delete"],"parameters":[{"name":"workflowName","loc":"path"}]},"CancelWorkflow":{"path":"/gs/workflow/{workflowName}","methods":["put"],"parameters":[{"name":"workflowName","loc":"path"}]},"DescirbeWorkflow":{"path":"/gs/workflow/{workflowName}","methods":["get"],"parameters":[{"name":"workflowName","loc":"path"}]},"DescribeWorkflows":{"path":"/gs/workflows","methods":["get"],"parameters":[]},"CreateCluster":{"path":"/clusters","methods":["post"],"parameters":[]},"DeleteCluster":{"path":"/clusters/{ClusterId}","methods":["delete"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"retain_all_resources","loc":"query"},{"name":"keep_slb","loc":"query"},{"name":"retain_resources","loc":"query"}]},"ScaleOutCluster":{"path":"/api/v2/clusters/{ClusterId}","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"ModifyCluster":{"path":"/api/v2/clusters/{ClusterId}","methods":["put"],"parameters":[{"name":"ClusterId","loc":"path"}]},"MigrateCluster":{"path":"/clusters/{cluster_id}/migrate","methods":["post"],"parameters":[{"name":"cluster_id","loc":"path"}]},"ScaleCluster":{"path":"/clusters/{ClusterId}","methods":["put"],"parameters":[{"name":"ClusterId","loc":"path"}]},"UpdateK8sClusterUserConfigExpire":{"path":"/k8s/{ClusterId}/user_config/expire","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DescribeClusterResources":{"path":"/clusters/{ClusterId}/resources","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DescribeClusterDetail":{"path":"/clusters/{ClusterId}","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DescribeUserQuota":{"path":"/quota","methods":["get"],"parameters":[]},"DescribeClustersV1":{"path":"/api/v1/clusters","methods":["get"],"parameters":[{"name":"name","loc":"query"},{"name":"cluster_type","loc":"query"},{"name":"page_size","loc":"query"},{"name":"page_number","loc":"query"},{"name":"profile","loc":"query"},{"name":"cluster_spec","loc":"query"},{"name":"region_id","loc":"query"}]},"DescribeExternalAgent":{"path":"/k8s/{ClusterId}/external/agent/deployment","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"PrivateIpAddress","loc":"query"}]},"DescribeClusterLogs":{"path":"/clusters/{ClusterId}/logs","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DescribeTaskInfo":{"path":"/tasks/{task_id}","methods":["get"],"parameters":[{"name":"task_id","loc":"path"}]},"DescribeKubernetesVersionMetadata":{"path":"/api/v1/metadata/versions","methods":["get"],"parameters":[{"name":"Region","loc":"query"},{"name":"ClusterType","loc":"query"},{"name":"KubernetesVersion","loc":"query"},{"name":"Profile","loc":"query"},{"name":"runtime","loc":"query"},{"name":"Mode","loc":"query"}]},"DescribeClusterUserKubeconfig":{"path":"/k8s/{ClusterId}/user_config","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"PrivateIpAddress","loc":"query"},{"name":"TemporaryDurationMinutes","loc":"query"}]},"DescribeClusterAddonUpgradeStatus":{"path":"/clusters/{ClusterId}/components/{ComponentId}/upgradestatus","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"ComponentId","loc":"path"}]},"DescribeClusters":{"path":"/clusters","methods":["get"],"parameters":[{"name":"name","loc":"query"},{"name":"clusterType","loc":"query"}]},"CreateAutoscalingConfig":{"path":"/cluster/{ClusterId}/autoscale/config/","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DescribeClusterV2UserKubeconfig":{"path":"/api/v2/k8s/{ClusterId}/user_config","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"PrivateIpAddress","loc":"query"}]},"DeleteClusterNodes":{"path":"/clusters/{ClusterId}/nodes","methods":["post","delete"],"parameters":[{"name":"ClusterId","loc":"path"}]},"RemoveClusterNodes":{"path":"/api/v2/clusters/{ClusterId}/nodes/remove","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"AttachInstances":{"path":"/clusters/{ClusterId}/attach","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DescribeClusterAttachScripts":{"path":"/clusters/{ClusterId}/attachscript","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DescribeClusterNodes":{"path":"/clusters/{ClusterId}/nodes","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"instanceIds","loc":"query"},{"name":"nodepool_id","loc":"query"},{"name":"state","loc":"query"},{"name":"pageSize","loc":"query"},{"name":"pageNumber","loc":"query"}]},"CreateClusterNodePool":{"path":"/clusters/{ClusterId}/nodepools","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DeleteClusterNodepool":{"path":"/clusters/{ClusterId}/nodepools/{NodepoolId}","methods":["delete"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"NodepoolId","loc":"path"},{"name":"force","loc":"query"}]},"ScaleClusterNodePool":{"path":"/clusters/{ClusterId}/nodepools/{NodepoolId}","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"NodepoolId","loc":"path"}]},"ModifyClusterNodePool":{"path":"/clusters/{ClusterId}/nodepools/{NodepoolId}","methods":["put"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"NodepoolId","loc":"path"}]},"DescribeClusterNodePools":{"path":"/clusters/{ClusterId}/nodepools","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DescribeClusterNodePoolDetail":{"path":"/clusters/{ClusterId}/nodepools/{NodepoolId}","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"NodepoolId","loc":"path"}]},"RepairClusterNodePool":{"path":"/clusters/{cluster_id}/nodepools/{nodepool_id}/repair","methods":["post"],"parameters":[{"name":"cluster_id","loc":"path"},{"name":"nodepool_id","loc":"path"}]},"UpgradeClusterNodepool":{"path":"/clusters/{ClusterId}/nodepools/{NodepoolId}/upgrade","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"NodepoolId","loc":"path"}]},"CancelClusterUpgrade":{"path":"/api/v2/clusters/{ClusterId}/upgrade/cancel","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"ResumeUpgradeCluster":{"path":"/api/v2/clusters/{ClusterId}/upgrade/resume","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"UpgradeCluster":{"path":"/api/v2/clusters/{ClusterId}/upgrade","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"PauseClusterUpgrade":{"path":"/api/v2/clusters/{ClusterId}/upgrade/pause","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"GetUpgradeStatus":{"path":"/api/v2/clusters/{ClusterId}/upgrade/status","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"}]},"CreateEdgeMachine":{"path":"/edge_machines","methods":["post"],"parameters":[]},"DeleteEdgeMachine":{"path":"/edge_machines/[edge_machineid]","methods":["delete"],"parameters":[{"name":"edge_machineid","loc":"path"},{"name":"force","loc":"query"}]},"DescribeEdgeMachineActiveProcess":{"path":"/edge_machines/[edge_machineid]/activeprocess","methods":["get"],"parameters":[{"name":"edge_machineid","loc":"path"}]},"DescribeEdgeMachineModels":{"path":"/edge_machines/models","methods":["get"],"parameters":[]},"DescribeEdgeMachines":{"path":"/edge_machines","methods":["get"],"parameters":[{"name":"hostname","loc":"query"},{"name":"model","loc":"query"},{"name":"online_state","loc":"query"},{"name":"life_state","loc":"query"},{"name":"page_size","loc":"query"},{"name":"page_number","loc":"query"}]},"DescribeEdgeMachineTunnelConfigDetail":{"path":"/edge_machines/[edge_machineid]/tunnelconfig","methods":["post"],"parameters":[{"name":"edge_machineid","loc":"path"}]},"EdgeClusterAddEdgeMachine":{"path":"/clusters/[clusterid]/attachedgemachine/[edge_machineid]","methods":["post"],"parameters":[{"name":"clusterid","loc":"path"},{"name":"edge_machineid","loc":"path"}]},"CreateTemplate":{"path":"/templates","methods":["post","put"],"parameters":[]},"DeleteTemplate":{"path":"/templates/{TemplateId}","methods":["delete"],"parameters":[{"name":"TemplateId","loc":"path"}]},"UpdateTemplate":{"path":"/templates/{TemplateId}","methods":["put"],"parameters":[{"name":"TemplateId","loc":"path"}]},"DescribeTemplates":{"path":"/templates","methods":["get"],"parameters":[{"name":"template_type","loc":"query"},{"name":"page_num","loc":"query"},{"name":"page_size","loc":"query"}]},"DescribeTemplateAttribute":{"path":"/templates/{TemplateId}","methods":["get"],"parameters":[{"name":"TemplateId","loc":"path"},{"name":"template_type","loc":"query"}]},"CreateTrigger":{"path":"/clusters/{cluster_id}/triggers","methods":["post"],"parameters":[{"name":"cluster_id","loc":"path"}]},"DeleteTrigger":{"path":"/clusters/[cluster_id]/triggers/[Id]","methods":["delete"],"parameters":[{"name":"cluster_id","loc":"path"},{"name":"Id","loc":"path"}]},"DescribeTrigger":{"path":"/clusters/{cluster_id}/triggers","methods":["get"],"parameters":[{"name":"cluster_id","loc":"path"},{"name":"Namespace","loc":"query"},{"name":"Type","loc":"query"},{"name":"Name","loc":"query"},{"name":"action","loc":"query"}]},"CreateKubernetesTrigger":{"path":"/triggers","methods":["post"],"parameters":[]},"DeleteKubernetesTrigger":{"path":"/triggers/revoke/{Id}","methods":["delete"],"parameters":[{"name":"Id","loc":"path"}]},"GetKubernetesTrigger":{"path":"/triggers/{ClusterId}","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"Namespace","loc":"query"},{"name":"Type","loc":"query"},{"name":"Name","loc":"query"},{"name":"action","loc":"query"}]},"InstallClusterAddons":{"path":"/clusters/{ClusterId}/components/install","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"UnInstallClusterAddons":{"path":"/clusters/{ClusterId}/components/uninstall","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DescribeClusterAddonMetadata":{"path":"/clusters/{cluster_id}/components/{component_id}/metadata","methods":["get"],"parameters":[{"name":"cluster_id","loc":"path"},{"name":"component_id","loc":"path"},{"name":"version","loc":"path"}]},"ModifyClusterAddon":{"path":"/clusters/{cluster_id}/components/{component_id}/config","methods":["post"],"parameters":[{"name":"cluster_id","loc":"path"},{"name":"component_id","loc":"path"}]},"UpgradeClusterAddons":{"path":"/clusters/{ClusterId}/components/upgrade","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"PauseComponentUpgrade":{"path":"/clusters/{clusterid}/components/{componentid}/pause","methods":["post"],"parameters":[{"name":"clusterid","loc":"path"},{"name":"componentid","loc":"path"}]},"ResumeComponentUpgrade":{"path":"/clusters/{clusterid}/components/{componentid}/resume","methods":["post"],"parameters":[{"name":"clusterid","loc":"path"},{"name":"componentid","loc":"path"}]},"CancelComponentUpgrade":{"path":"/clusters/{clusterId}/components/{componentId}/cancel","methods":["post"],"parameters":[{"name":"clusterId","loc":"path"},{"name":"componentId","loc":"path"}]},"DescribeAddons":{"path":"/clusters/components/metadata","methods":["get"],"parameters":[{"name":"region","loc":"query"},{"name":"cluster_type","loc":"query"},{"name":"cluster_profile","loc":"query"},{"name":"cluster_spec","loc":"query"},{"name":"cluster_version","loc":"query"}]},"DescribeClusterAddonsUpgradeStatus":{"path":"/clusters/{ClusterId}/components/upgradestatus","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"componentIds","loc":"query"}]},"DescribeClusterAddonsVersion":{"path":"/clusters/{ClusterId}/components/version","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"}]},"UntagResources":{"path":"/tags","methods":["delete"],"parameters":[{"name":"region_id","loc":"query"},{"name":"resource_ids","loc":"query"},{"name":"resource_type","loc":"query"},{"name":"tag_keys","loc":"query"},{"name":"all","loc":"query"}]},"TagResources":{"path":"/tags","methods":["put"],"parameters":[]},"ModifyClusterTags":{"path":"/clusters/{ClusterId}/tags","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"ListTagResources":{"path":"/tags","methods":["get"],"parameters":[{"name":"resource_ids","loc":"query"},{"name":"resource_type","loc":"query"},{"name":"region_id","loc":"query"},{"name":"tags","loc":"query"},{"name":"next_token","loc":"query"}]},"ModifyClusterConfiguration":{"path":"/clusters/{ClusterId}/configuration","methods":["put"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DeletePolicyInstance":{"path":"/clusters/{cluster_id}/policies/{policy_name}","methods":["delete"],"parameters":[{"name":"cluster_id","loc":"path"},{"name":"policy_name","loc":"path"},{"name":"instance_name","loc":"query"}]},"ModifyPolicyInstance":{"path":"/clusters/{cluster_id}/policies/{policy_name}","methods":["put"],"parameters":[{"name":"cluster_id","loc":"path"},{"name":"policy_name","loc":"path"}]},"DescribePolicies":{"path":"/policies","methods":["get"],"parameters":[]},"DescribePolicyDetails":{"path":"/policies/{policy_name}","methods":["get"],"parameters":[{"name":"policy_name","loc":"path"}]},"DescribePolicyGovernanceInCluster":{"path":"/clusters/{cluster_id}/policygovernance","methods":["get"],"parameters":[{"name":"cluster_id","loc":"path"}]},"DescribePolicyInstances":{"path":"/clusters/{cluster_id}/policies","methods":["get"],"parameters":[{"name":"cluster_id","loc":"path"},{"name":"policy_name","loc":"query"},{"name":"instance_name","loc":"query"}]},"DescribePolicyInstancesStatus":{"path":"/clusters/{cluster_id}/policies/status","methods":["get"],"parameters":[{"name":"cluster_id","loc":"path"}]},"DeployPolicyInstance":{"path":"/clusters/{cluster_id}/policies/{policy_name}","methods":["post"],"parameters":[{"name":"cluster_id","loc":"path"},{"name":"policy_name","loc":"path"}]},"FixNodePoolVuls":{"path":"/clusters/{cluster_id}/nodepools/{nodepool_id}/vuls/fix","methods":["post"],"parameters":[{"name":"cluster_id","loc":"path"},{"name":"nodepool_id","loc":"path"}]},"DescribeNodePoolVuls":{"path":"/clusters/{cluster_id}/nodepools/{nodepool_id}/vuls","methods":["get"],"parameters":[{"name":"cluster_id","loc":"path"},{"name":"nodepool_id","loc":"path"},{"name":"necessity","loc":"query"}]},"PauseTask":{"path":"/tasks/{task_id}/pause","methods":["post"],"parameters":[{"name":"task_id","loc":"path"}]},"ResumeTask":{"path":"/tasks/{task_id}/resume","methods":["post"],"parameters":[{"name":"task_id","loc":"path"}]},"CancelTask":{"path":"/tasks/{task_id}/cancel","methods":["post"],"parameters":[{"name":"task_id","loc":"path"}]},"DescribeClusterEvents":{"path":"/clusters/{ClusterId}/events","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"page_size","loc":"query"},{"name":"page_number","loc":"query"},{"name":"task_id","loc":"query"}]},"DescribeClusterTasks":{"path":"/clusters/{cluster_id}/tasks","methods":["get"],"parameters":[{"name":"cluster_id","loc":"path"}]},"ModifyNodePoolNodeConfig":{"path":"/clusters/{ClusterId}/nodepools/{NodepoolId}/node_config","methods":["put"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"NodepoolId","loc":"path"}]},"RemoveNodePoolNodes":{"path":"/clusters/{ClusterId}/nodepools/{NodepoolId}/nodes","methods":["delete"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"NodepoolId","loc":"path"},{"name":"release_node","loc":"query"},{"name":"drain_node","loc":"query"},{"name":"nodes","loc":"query"},{"name":"instance_ids","loc":"query"}]},"SyncClusterNodePool":{"path":"/clusters/{ClusterId}/sync_nodepools","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DescribeSubaccountK8sClusterUserConfig":{"path":"/k8s/{ClusterId}/users/{Uid}/user_config","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"Uid","loc":"path"},{"name":"PrivateIpAddress","loc":"query"},{"name":"TemporaryDurationMinutes","loc":"query"}]},"ScanClusterVuls":{"path":"/clusters/{cluster_id}/vuls/scan","methods":["post"],"parameters":[{"name":"cluster_id","loc":"path"}]},"DescribeClusterVuls":{"path":"/clusters/{cluster_id}/vuls","methods":["get"],"parameters":[{"name":"cluster_id","loc":"path"}]},"AttachInstancesToNodePool":{"path":"/clusters/{ClusterId}/nodepools/{NodepoolId}/attach","methods":["post"],"parameters":[{"name":"ClusterId","loc":"path"},{"name":"NodepoolId","loc":"path"}]},"DescribeClusterAddonInstance":{"path":"/clusters/{ClusterID}/components/{AddonName}/instance","methods":["get"],"parameters":[{"name":"ClusterID","loc":"path"},{"name":"AddonName","loc":"path"}]},"CheckControlPlaneLogEnable":{"path":"/clusters/{ClusterId}/controlplanelog","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"}]},"UpdateControlPlaneLog":{"path":"/clusters/{ClusterId}/controlplanelog","methods":["put"],"parameters":[{"name":"ClusterId","loc":"path"}]},"DescribeUserClusterNamespaces":{"path":"/api/v2/k8s/{ClusterId}/namespaces","methods":["get"],"parameters":[{"name":"ClusterId","loc":"path"}]}}