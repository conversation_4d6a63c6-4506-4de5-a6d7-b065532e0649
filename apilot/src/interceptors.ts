import { AxiosRequestConfig } from "axios";
import { APIOptions } from "./types";

export function customUrlMapperInterceptor(action: string, config: APIOptions) {
  // if (config.customUrlMap?.[action]) config.url = config.customUrlMap[action];
  return config;
}

export function mockUrlMapperInterceptor(action: string, config: APIOptions) {
  // if (config.mockUrlMap?.[action]) config.url = config.mockUrlMap[action];
  return config;
}

export function defaultUrlMapperInterceptor(
  value: string,
  defaultMapper: Object,
  config: APIOptions
) {
  // let url = defaultMapper.path;
  // if (!url) return config;
  // defaultMapper.parameters?.forEach(param => {
  //   if (param.loc === 'path') url = url.replace(`{${param.name}}`, value);
  // });
  // config.url = url;
  // config.method = config.method || defaultMapper.methods?.[0];

  return config;
}

export function atlasInterceptor(
  action: string,
  requestParams: any,
  config: APIOptions
) {
  config.url = "/data/custom.json";
  config.method = "POST";
  // config.data = {
  //   product: config.productCode,
  //   action,
  //   params: requestParams.params,
  //   // params: JSON.stringify(requestParams.params),
  //   content: JSON.stringify(requestParams.content),
  // };
  return config;
}

export function devAtlasInterceptor(
  action: string,
  port: string,
  requestParams: any,
  config: APIOptions
) {
  config.url = `http://127.0.0.1:${port}/actions/${action}`;
  config.method = "POST";
  config.headers["Content-Type"] = "application/x-www-form-urlencoded";
  const params = new URLSearchParams();
  params.append("action", action);
  if (config.region) params.append("region", config.region);
  if (requestParams.params)
    params.append("params", JSON.stringify(requestParams.params));
  params.append("sec_token", (window as any)?.ALIYUN_CONSOLE_CONFIG?.SEC_TOKEN);
  config.data = params;
  // config.data = {
  //   params: JSON.stringify(requestParams.params),
  //   sec_token: (window as any)?.ALIYUN_CONSOLE_CONFIG?.SEC_TOKEN,
  //   region: config.region,
  // };
  return config;
}

export function fecsAtlasInterceptor(
  product: string,
  action: string,
  requestParams: any,
  config: APIOptions
) {
  const isPreEnv = window.location.host.indexOf("pre") > -1;
  const fecsHost = isPreEnv ? "pre-fecs" : "fecs";
  config.url = `https://${fecsHost}.console.aliyun.com/data/publicCustom.json`;
  config.method = "POST";
  config.headers["Content-Type"] = "application/x-www-form-urlencoded";
  const params = new URLSearchParams();
  params.append("product", product);
  params.append("action", action);
  if (config.region) params.append("region", config.region);
  if (requestParams.params)
    params.append("params", JSON.stringify(requestParams.params));
  params.append("sec_token", (window as any)?.ALIYUN_CONSOLE_CONFIG?.SEC_TOKEN);
  config.data = params;
  return config;
}

export function customInterceptor(
  requestParams: any,
  config: AxiosRequestConfig
) {
  config.params = requestParams.params;
  config.data = requestParams.content;
  return config;
}

export function mockInterceptor(
  productCode: string,
  action: string,
  config: AxiosRequestConfig
) {
  config.url = `https://oneapi.alibaba-inc.com/mock/${productCode}/${action}`;
  config.method = "POST";
  return config;
}

export function preFecsOpenApiInterceptor(
  product: string,
  action: string,
  requestParams: any,
  config: APIOptions
) {
  config.url = `https://pre-fecs.console.aliyun.com/data/api.json`;
  config.headers["Content-Type"] = "application/x-www-form-urlencoded";
  config.method = "POST";
  const params = new URLSearchParams();
  params.append("product", product);
  params.append("action", action);
  if (requestParams.params)
    params.append("params", JSON.stringify(requestParams.params));
  // params.append("sec_token", (window as any)?.ALIYUN_CONSOLE_CONFIG?.SEC_TOKEN);
  config.data = params;

  return config;
}
