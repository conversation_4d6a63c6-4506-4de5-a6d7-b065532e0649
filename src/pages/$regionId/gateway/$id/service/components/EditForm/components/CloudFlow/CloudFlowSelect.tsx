import { Truncate, Icon, Input, intl, LinkButton, Button } from '@ali/cnd';
import React, { useState, useRef } from 'react';
import ExtendSelect from '~/components/shared/ExtendSelect';
import RefreshButton from '~/components/shared/RefreshButton';
import services from '~/utils/services';
import ExternalLink from '~/components/shared/ExternalLink';
import CachedData from '~/utils/cacheData';
import { SourceTypes } from '~/constants';

const CloudFlowSelect = ({ gatewayId, ...reset }) => {
  const [refreshIndex, setRefreshIndex] = useState(0);
  const selectRef = useRef(null);

  const getList = async ({ searchValue, currentPage }) => {
    const { items = [] } = await services.ListExternalServices({
      params: {
        gatewayId,
        sourceType: SourceTypes.CloudFlow,
        limit: 100,
        nameLike: searchValue,
        importableOnly: true,
      },
    });
    const list = [];
    (items || []).forEach((item) => {
      (item?.services || []).forEach((service) => {
        list.push({
          label: service?.name,
          value: service?.name,
          expressType: service?.expressType,
        });
      });
    });
    return {
      data: list,
      currentPage: 1,
      total: list.length,
      pageSize: 200,
    };
  };

  const handleItemRender = (item) => {
    return (
      <div>
        <Truncate showTooltip type="width" threshold={300}>
          {item?.label}
        </Truncate>
        <div style={{ display: 'inline-block', float: 'right' }}>
          <ExternalLink
            className="ml-4"
            url={CachedData.confLink('feature:cloudFlow:url', {
              regionId: window.regionId,
              cloudFlowName: item?.label,
            })}
            label={intl('apigw.components.api-operations-policy.PolicyEnvList.View')}
          />
        </div>
      </div>
    );
  };

  return (
    <>
      <ExtendSelect
        hasClear
        hoverShowClear
        ref={selectRef}
        popupClassName="env-select-list"
        className="full-width"
        itemRender={handleItemRender}
        fetchData={getList}
        refreshIndex={refreshIndex}
        showSearch
        popupAutoFocus
        customHeader={
          <div>
            <div className="pl-16 align-center justify-between mt-8">
              <ExternalLink
                className="ml-4"
                url={`//fcnext.console.aliyun.com/${window.regionId}/functions/create`}
                label={intl('apigw.components.selectScroll.popupContent.CreateAFunction')}
              />
              <RefreshButton
                type="primary"
                label={intl('button.refresh')}
                className="ml-8 mr-8 f-w-400"
                iconSize="xs"
                handler={() => {
                  selectRef?.current?.handleSearch('');
                }}
              />
            </div>
            <div style={{ padding: '8px 4px' }}>
              <Input
                innerBefore={<Icon type="search" style={{ margin: '6px 0px 6px 12px' }} />}
                style={{ width: '100%' }}
                onChange={(v) => {
                  selectRef?.current?.handleSearch(v);
                }}
              />
            </div>
          </div>
        }
        {...reset}
      />
    </>
  );
};

export default CloudFlowSelect;
