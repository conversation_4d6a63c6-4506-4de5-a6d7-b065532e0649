import React, { useRef, forwardRef, useImperativeHandle, useEffect, useMemo } from 'react';
import { intl, Balloon, Form, Grid, Icon, Input, Field } from '@ali/cnd';
import CustomDragged from '~/components/shared/CustomDragged';
import { includes, isEmpty } from 'lodash';
import CloudFlowSelect from './CloudFlowSelect';
import CustomTableForm, { registerColumns } from '~/components/shared/CustomTableForm';
import { expressTypeColumn, nameColumn } from './columns';

registerColumns({
  name: nameColumn,
  expressType: expressTypeColumn,
});

const { Row, Col } = Grid;
const CloudFlow = forwardRef((props: any, ref) => {
  const { curData = {}, type, gatewayId: _gatewayId } = props;
  const field = Field.useField();
  const { init, getValues, setValues } = field;
  const customDraggedRef = useRef(null);

  useImperativeHandle(ref, () => ({
    validate: async () => {
      return new Promise((resolve) => {
        field.validate((errors) => {
          resolve(errors);
        });
      });
    },
    getValue,
    createService: () => handleSubmit(),
  }));

  useEffect(() => {
    if (!isEmpty(curData)) {
      const { name, expressType } = curData;
      setValues({
        serviceConfigs: [{ name, expressType }],
      });
    } else {
      setValues({
        serviceConfigs: [{ name: '', expressType: '' }],
      });
    }
  }, [JSON.stringify(curData)]);

  const getValue = () => {
    const formValues: any = getValues() || {};
    return {
      ...formValues,
    };
  };

  const handleSubmit = () => {
    return new Promise((resolve, reject) => {
      field.validate((errors, values: any) => {
        if (errors) {
          resolve(null);
        } else {
          let values = getValue();
          resolve({
            ...values,
          });
        }
      });
    });
  };

  // const columns = useMemo(() => {
  //   return [
  //     {
  //       dataIndex: 'name',
  //       valueType: 'string',
  //       colSpan: 12,
  //       cell: ({ record, onItemChange, onChange, ...reset }) => {
  //         console.log(reset);
  //         return (
  //           <CloudFlowSelect
  //             gatewayId={_gatewayId}
  //             onChange={(value, _, item) => {
  //               onItemChange('expressType', item?.expressType);
  //               onChange(value);
  //             }}
  //             {...reset}
  //           />
  //         );
  //       },
  //     },
  //     {
  //       dataIndex: 'expressType',
  //       valueType: 'string',
  //       colSpan: 11,
  //       cell: ({ record, onItemChange, ...reset }) => {
  //         return <Input disabled={true} placeholder={'请选择'} {...reset} />;
  //       },
  //     },
  //   ];
  // }, [JSON.stringify(curData)]);

  const columnTypes = useMemo(() => {
    return [{ type: 'name', options: { gatewayId: _gatewayId } }, 'expressType'];
  }, [JSON.stringify(curData)]);

  return (
    <Form field={field} labelAlign="left">
      <CustomTableForm
        {...init('serviceConfigs', {
          rules: [
            {
              required: true,
              message: intl('apigw.CloudFlow.columns.PleaseSelectCloudFlow'),
            },
            {
              validator: async (rule, value, callback) => {
                const { errors, values } = await customDraggedRef.current.validate();
                console.log(errors, values, 'errors');
                if (errors) {
                  callback(intl('apigw.CloudFlow.columns.PleaseSelectCloudFlow'));
                } else {
                  callback();
                }
              },
            },
          ],
        })}
        ref={customDraggedRef}
        columnTypes={columnTypes}
        defaultRowData={{ name: '', expressType: '' }}
        key={JSON.stringify(columnTypes)}
      />
      {/* <Form.Item className="mb-0">
        <Row gutter="16" className="mb-8">
          <Col span={12}>
            <span>{'工作流程'}</span>
          </Col>
          <Col span={11}>
            <span className="ml-42">{'执行模式'}</span>
          </Col>
        </Row>

        <CustomDragged
          {...init('serviceConfigs', {
            initValue: [{ name: undefined, expressType: undefined }],
            rules: [
              {
                required: true,
                message: '请选择工作流名称',
              },
              {
                validator: async (rule, value, callback) => {
                  const { errors, values } = await customDraggedRef.current.validate();
                  console.log(errors, values, 'errors');
                  if (errors) {
                    callback(intl('请选择工作流名称'));
                  } else {
                    callback();
                  }
                },
              },
            ],
          })}
          ref={customDraggedRef}
          columns={columns as any}
          key={JSON.stringify(curData)}
        />
      </Form.Item> */}
    </Form>
  );
});
export default CloudFlow;
