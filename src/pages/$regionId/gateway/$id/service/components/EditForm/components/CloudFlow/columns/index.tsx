import { createColumn } from '~/components/shared/CustomTableForm';
import CloudFlowSelect from '../CloudFlowSelect';
import React from 'react';
import { intl, Input, Balloon, Icon } from '@ali/cnd';

export const nameColumn = createColumn(
  {
    dataIndex: 'name',
    title: intl('apigw.CloudFlow.columns.CloudFlow'),
    required: true,
    colSpan: 12,
  },
  ({ record, onItemChange, onChange, ...reset }, options) => {
    const { gatewayId } = options;
    console.log(options, 'options');
    return (
      <CloudFlowSelect
        gatewayId={gatewayId}
        onChange={(value, _, item) => {
          onItemChange('expressType', item?.expressType);
          onChange(value);
        }}
        {...reset}
      />
    );
  },
);

export const expressTypeColumn = createColumn(
  {
    dataIndex: 'expressType',
    title: (
      <span>
        {intl('apigw.components.BasicInfo.CallMethod')}

        <Balloon
          type="primary"
          align="t"
          trigger={<Icon className="ml-4" type="info" size={14} />}
          closable={false}
        >
          <div>
            <div>
              {intl('apigw.components.BasicInfo.StartexecutionAsynchronousCallStandardMode')}
            </div>
            <div>
              {intl('apigw.components.BasicInfo.StartsyncexecutionSynchronousCallFastMode')}
            </div>
          </div>
        </Balloon>
      </span>
    ),
    required: true,
    colSpan: 12,
  },
  ({ record, onItemChange, ...reset }) => {
    return (
      <Input
        disabled={true}
        placeholder={intl('apigw.CloudFlow.columns.PleaseSelect')}
        {...reset}
      />
    );
  },
);
