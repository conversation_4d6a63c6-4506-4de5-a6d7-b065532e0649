import React, { useEffect } from 'react';
import { createAlfaApp } from '@ali/xconsole/alfa';

const DocQuickStart = createAlfaApp({
  name: '@ali/alfa-cloud-apigateway-widget-document-quickstart',
  className: 'widget-document-quickstart',
})

export default (props) => {
  const { visible, setVisible } = props;
  useEffect(() => {
    const isQuickStartVisible = localStorage && localStorage.getItem('document-quickstart-visible');
      if (isQuickStartVisible && isQuickStartVisible === 'true') {
        setVisible(true);
      } else {
        setVisible(false);
      }
  }, []);
  return (
    visible ? <DocQuickStart {...props} /> : null
  )
}
