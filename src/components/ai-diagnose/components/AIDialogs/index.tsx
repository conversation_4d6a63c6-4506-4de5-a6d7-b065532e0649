import { Balloon, Button, Icon, Input, intl, Loading } from '@ali/cnd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { CommonErrorBoundary } from '../../../shared/ErrorFallback';
import { SlideDrawer } from '~/components/shared/SlideDrawer';
import { DialogItem } from '~/components/api-manage/apiAi/AIModelDebug/AIDialogs';
import { useScrollToBottom } from '~/components/api-manage/apiAi/AIModelDebug/hook';
import { DEBUG_STATUS } from '~/components/api-manage/apiAi/AIModelDebug/Response';
import { findLastIndex, concat, last, map, slice, size, get, initial } from 'lodash';
import { MODEL_DEBUG_ROLE } from '~/components/api-manage/apiAi/AIModelDebug';
import CachedData from '~/utils/cacheData';

const AIDialogs = ({ visible, onClose, currentSlsInfo }) => {
  const dialogRef = useRef(null);
  const abortRef = useRef({ isAbort: false });
  const { scrollRef, scrollToBottom } = useScrollToBottom(dialogRef);
  const [status, setStatus] = useState('enabled');
  const [prompt, setPrompt] = useState('');
  const [dialogs, setDialogs] = useState([]);
  useEffect(() => {
    onSend();
  }, []);

  const disabledSendRequest = useMemo(() => {
    //禁止提问状态或非流式请求正在提问中
    return status === 'disable';
  }, [status]);
  const handleReAgain = () => {
    onSend('reAgain');
  };

  const handleClear = () => {
    setDialogs([]);
  };

  const addSystemPrompt = ({ prompt, operationType }) => {
    if (operationType === 'reAgain' || !prompt) {
      return concat(dialogs, []);
    }
    return concat(dialogs, [
      {
        role: MODEL_DEBUG_ROLE.user,
        content: prompt,
      },
    ]);
  };
  const onSend = async (type?) => {
    setPrompt('');
    setStatus('running');
    abortRef.current.isAbort = false;
    let _prompt = '';
    if (type === 'reAgain') {
      let index = findLastIndex(dialogs, {
        role: MODEL_DEBUG_ROLE.user,
      });

      _prompt = dialogs[index]?.content || '';
    }
    await onSendRequest({
      prompt: type === 'reAgain' ? _prompt : prompt,
      operationType: type,
    });
  };

  const onSendRequest = async (params) => {
    const { prompt, operationType } = params;
    let history = map(
      concat(dialogs).filter((item) => item.content !== undefined),
      (i) => {
        const { role, content } = i;
        return {
          role,
          content,
        };
      },
    );

    if (operationType === 'reAgain') {
      const lastHistory = last(history);
      if (get(lastHistory, 'role') === MODEL_DEBUG_ROLE.assistant) {
        history = initial(history);
      }
    }
    const requestParams = {
      agentName: 'slsLogDiagnose',
      incrementalOutput: true,
      bizParams: { ...currentSlsInfo },
      prompt: prompt,
      history: history,
      outputLanguage: CachedData.lang,
    };
    const _newDialogs = addSystemPrompt({
      prompt,
      operationType,
    });
    setDialogs(_newDialogs);
    setTimeout(() => {
      scrollToBottom();
    }, 300);
    return await streamRequest(requestParams, operationType);
  };

  const streamRequest = (requestParams: any, operationType) => {
    return fetch('/tool/sse/get.json', {
      method: 'POST',
      mode: 'same-origin',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        product: 'APIG',
        action: 'InvokeAIAgent',
        region: window.regionId,
        content: JSON.stringify(requestParams),
        sec_token: window.ALIYUN_CONSOLE_CONFIG.SEC_TOKEN,
      }),
    })
      .then(function (res) {
        return res.json();
      })
      .then(async function (result) {
        const headers = result.data.headers;
        const raw =
          headers['Content-Type'] == 'application/json'
            ? result.data.body
            : headers['Content-Type'] == 'application/x-www-form-urlencoded'
              ? new URLSearchParams(JSON.parse(result.data.body))
              : null;
        const myHeaders = new Headers();
        Object.entries(headers).forEach((d) => myHeaders.append(d[0], d[1] as string));
        const aiResponse = (await fetch(result.data.uri.replace(/\/$/, '') + result.data.path, {
          method: 'POST',
          headers: myHeaders,
          body: raw,
          redirect: 'follow',
        })) as any;
        console.log('stream requestId ------ ', aiResponse?.headers?.get('x-acs-request-id'));
        const reader = aiResponse.body.getReader();
        if (abortRef?.current?.isAbort === true) {
          try {
            await reader.cancel(); // 尝试取消读取
          } catch (error) {
            console.error('Error cancelling stream:', error);
          }
        }
        let trunk: any;
        let _operationType = operationType;

        while (!abortRef?.current?.isAbort && !(trunk = await reader.read()).done) {
          const decoder = new TextDecoder();
          // console.log('===', decoder.decode(trunk.value));
          const chunk = decoder.decode(trunk.value);
          // 将 chunk 按行分割，因为每一行可能是一个独立的事件
          const lines = chunk.split('\n');
          for (const line of lines) {
            if (line.startsWith('data:') && !line.startsWith('data:[DONE]')) {
              try {
                const data = JSON.parse(line.substring(5));
                const body = data.body;
                if (body) {
                  const decodedBody = body;
                  // console.log('Parsed Body:', decodedBody);
                  // const content = decodedBody.choices[0].delta.content;
                  // if (content) {
                  //   console.log('Assistant Content:', content);
                  // }
                  try {
                    let _decodedBody = JSON.parse(decodedBody);
                    let responseContent = _decodedBody?.choices?.[0]?.delta?.content || '';
                    //@ts-ignore
                    setDialogs((pre) => {
                      let _last: any = last(pre);
                      if (
                        _last?.role !== MODEL_DEBUG_ROLE.assistant ||
                        _operationType === 'reAgain'
                      ) {
                        _operationType = '';
                        return [
                          ...pre,
                          {
                            role: MODEL_DEBUG_ROLE.assistant,
                            content: responseContent,
                            stream: true,
                            assistantContent: decodedBody,
                          },
                        ];
                      } else {
                        let _decodedBodyLast = JSON.parse(_last.assistantContent);
                        let lastContent = _decodedBodyLast?.choices?.[0]?.delta?.content || '';
                        if (_decodedBody.choices[0])
                          _decodedBody.choices[0].delta.content = lastContent + responseContent;
                        else
                          _decodedBody.choices[0] = {
                            delta: {
                              content: lastContent + responseContent,
                            },
                          };
                        console.log(lastContent, 'lastContent');
                        // console.log('====_decodedBody', _decodedBody, pre);
                        return concat(slice(pre, 0, size(pre) - 1), {
                          role: MODEL_DEBUG_ROLE.assistant,
                          content: lastContent,
                          stream: true,
                          assistantContent: JSON.stringify(_decodedBody),
                        });
                      }
                    });
                  } catch (error) {
                    //服务错误时
                    //@ts-ignore
                    setDialogs((pre) => {
                      return [
                        ...pre,
                        {
                          role: MODEL_DEBUG_ROLE.assistant,
                          content: decodedBody,
                          stream: true,
                          assistantContent: decodedBody,
                        },
                      ];
                    });
                  }
                }
              } catch (error) {
                console.error('Error parsing data:', error);
                //@ts-ignore
                setDialogs((pre) => {
                  return [
                    ...pre,
                    {
                      role: MODEL_DEBUG_ROLE.assistant,
                      content: '',
                      stream: true,
                      assistantContent: '',
                    },
                  ];
                });
              }
              scrollToBottom();
            }
          }
        }
        setStatus('disable');
      });
  };

  if (!visible) return null;
  return (
    <SlideDrawer
      isShowing={true}
      width={1000}
      title={intl('apigw.components.AIDialogs.AiDiagnosis')}
      cancelText={intl('apigw.apiAi.AIModelDebug.Close')}
      onCancel={onClose}
      onClose={onClose}
      bodyStyle={{ overflow: 'hidden' }}
    >
      <CommonErrorBoundary>
        <div className="flex flex-col bg-[#F6F6F6] h-full">
          <div className="flex-1 px-4 pb-16" style={{ overflowY: 'auto' }} ref={scrollRef}>
            {dialogs.map((dialog) => (
              <DialogItem {...dialog} isShowToken={false} />
            ))}
          </div>
          <div className="bg-[#F6F6F6] px-4">
            <Loading
              className="w-full"
              visible={status === DEBUG_STATUS.running}
              indicator={
                <div className="load-container debug-loading">
                  <div className="loader">
                    {intl('apigw.apiAi.AIModelDebug.Response.InTheRequest')}
                  </div>
                </div>
              }
              size="medium"
            >
              <Input.TextArea
                style={{ width: '100%' }}
                value={prompt}
                onChange={(val) => {
                  setPrompt(val);
                  setStatus(!!val ? 'enabled' : 'disable');
                }}
                autoFocus
                placeholder={intl('apigw.apiAi.AIModelDebug.Response.UseEnterToSendUse')}
                autoHeight={{ minRows: 1, maxRows: 3 }}
                maxLength={300}
                showLimitHint
                disabled={status === 'running'}
                onKeyDown={async (e) => {
                  //enter 发送
                  if (e.key === 'Enter' && !e.metaKey) {
                    e.preventDefault();
                    if (!prompt) return;
                    if (disabledSendRequest) return;
                    onSend();
                  }
                  // command+enter 换行
                  if (e.key === 'Enter' && e.metaKey) {
                    e.preventDefault();
                    setPrompt(prompt + '\n');
                  }
                }}
              ></Input.TextArea>
            </Loading>
            <div className="flex justify-between items-center my-2">
              <div className="flex flex-col">
                <div>
                  <Balloon
                    align="t"
                    closable={false}
                    trigger={
                      <Button
                        className="mr-8"
                        onClick={handleReAgain}
                        size="small"
                        disabled={status === DEBUG_STATUS.running}
                      >
                        <Icon type="redo" />
                      </Button>
                    }
                  >
                    {intl('apigw.apiAi.AIModelDebug.Response.DoItAgain')}
                  </Balloon>
                  <Balloon
                    align="t"
                    closable={false}
                    trigger={
                      <Button
                        className="mr-8 isOnlyIcon"
                        onClick={handleClear}
                        size="small"
                        disabled={status === DEBUG_STATUS.running}
                      >
                        <img
                          style={{ display: 'inline-block' }}
                          src="https://img.alicdn.com/imgextra/i4/O1CN01P95STu1xPk5gZM20q_!!6000000006436-55-tps-12-12.svg"
                        />
                      </Button>
                    }
                  >
                    {intl('apigw.apiAi.AIModelDebug.Response.ClearMemory')}
                  </Balloon>
                </div>
              </div>
              <Button
                disabled={disabledSendRequest}
                type="primary"
                onClick={() => {
                  if (status === 'running') {
                    abortRef.current.isAbort = true;
                    //非请求中结束问答或者出错，需要允许重新发送
                    setStatus(DEBUG_STATUS.disable);
                  } else {
                    onSend();
                  }
                }}
                size="small"
              >
                <img
                  className="mr-4"
                  style={{ display: 'inline-block' }}
                  src={
                    status === 'disable'
                      ? 'https://img.alicdn.com/imgextra/i3/O1CN01tjRJTJ1vV7TX1dUwZ_!!6000000006177-55-tps-12-12.svg'
                      : status === 'enabled'
                        ? 'https://img.alicdn.com/imgextra/i2/O1CN01xZBhi11qinDepWpoM_!!6000000005530-55-tps-12-12.svg'
                        : 'https://img.alicdn.com/imgextra/i3/O1CN01mbOKTB1Qu4yCJRXvi_!!6000000002035-55-tps-12-12.svg'
                  }
                />

                {status === 'running'
                  ? intl('apigw.createApiDialog.ai.Stop')
                  : intl('apigw.apiAi.AIModelDebug.Response.Send')}
              </Button>
            </div>
          </div>
        </div>
      </CommonErrorBoundary>
    </SlideDrawer>
  );
};

export default AIDialogs;
