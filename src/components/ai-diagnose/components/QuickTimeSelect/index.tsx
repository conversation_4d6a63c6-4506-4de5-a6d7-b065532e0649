import React, { useState, useEffect } from 'react';
import { Radio, DatePicker, intl } from '@ali/cnd';
import moment from 'moment';
const RadioGroup = Radio.Group;
const { RangePicker } = DatePicker;

const dataSource = [
  {
    label: intl('mse.common.1_hour'),
    value: 'last_1_hours',
  },
  {
    label: intl('mse.common.12_hour'),
    value: 'last_12_hours',
  },
  {
    label: intl('mse.common.1_day'),
    value: 'last_24_hours',
  },
  {
    label: intl('mse.common.3_day'),
    value: 'last_3_days',
  },
];

const QuickTimeSelect = (props) => {
  const { currentQuickTime, startTime, endTime, changeTimeRange } = props;
  const [startTimeMoment, setStartTimeMoment] = useState();
  const [endTimeMoment, setEndTimeMoment] = useState();
  const [format, setFormat] = useState('YYYY-MM-DD');

  useEffect(() => {
    if (currentQuickTime && !startTime && !endTime) {
      onQuickTimeChange(currentQuickTime);
    }
    startTime && setStartTimeMoment(moment(startTime) as any);
    endTime && setEndTimeMoment(moment(endTime) as any);
  }, [currentQuickTime, startTime, endTime]);

  useEffect(() => {
    const lang = window?.aliwareGetCookieByKeyName('aliyun_lang') || 'zh';
    lang !== 'zh' && setFormat('LL');
  }, [window?.aliwareGetCookieByKeyName('aliyun_lang')]);
  const onQuickTimeChange = (value) => {
    let timeTypeArr = value.split('_');
    // 定义的快捷时间value的格式--last_num_type  type(minutes/hours/days/weeks/months/years)
    if (value && timeTypeArr.length === 3) {
      let type = timeTypeArr[2];
      let num = parseInt(timeTypeArr[1]);
      let start = 0;
      let end = moment().valueOf();
      switch (type) {
        case 'minutes':
          start = moment().subtract(num, 'minutes').valueOf();
          break;
        case 'hours':
          start = moment().subtract(num, 'hours').valueOf();
          break;
        case 'days':
          start = moment().subtract(num, 'days').valueOf();
          break;
        case 'weeks':
          start = moment().subtract(num, 'weeks').valueOf();
          break;
        case 'months':
          start = moment().subtract(num, 'months').valueOf();
          break;
        case 'years':
          start = moment().subtract(num, 'years').valueOf();
          break;
      }
      changeTimeRange({
        startTime: start,
        endTime: end,
        currentQuickTime: value,
      });
    }
  };
  const onChangeCustomTime = (value) => {
    changeTimeRange({
      startTime: value[0].valueOf(),
      endTime: value[1].valueOf(),
      currentQuickTime: value,
    });
  };

  const disabledDate = (date) => {
    return (
      date.valueOf() < moment().startOf('day').subtract(3, 'day').valueOf() ||
      date.valueOf() > moment().startOf('day').valueOf()
    );
  };

  return (
    <div className="quick-time-select">
      <RadioGroup
        dataSource={dataSource}
        shape="button"
        size="medium"
        value={currentQuickTime}
        onChange={onQuickTimeChange}
      />
      <div style={{ marginLeft: 20, display: 'inline-block' }}>
        <span style={{ marginRight: 10, color: '#555', fontWeight: 700 }}>
          {intl('mse.common.custom_time')}
        </span>
        <RangePicker
          showTime={{
            format: 'HH:mm:ss',
          }}
          format={format}
          value={[startTimeMoment, endTimeMoment]}
          disabledDate={disabledDate}
          onOk={onChangeCustomTime}
          onChange={(value) => {
            setStartTimeMoment(value[0]);
            setEndTimeMoment(value[1]);
          }}
        />
      </div>
    </div>
  );
};

export default QuickTimeSelect;
