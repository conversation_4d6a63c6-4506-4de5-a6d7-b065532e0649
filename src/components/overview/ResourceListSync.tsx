import React, { useEffect, useState } from 'react';
import { Actions, Balloon, Button, Icon, Table, intl } from '@ali/cnd';
import { get, isEmpty, isNil, map, some, includes, first } from 'lodash';
import ExternalLink from '../shared/ExternalLink';
import services from '~/utils/services';
import CachedData from '~/utils/cacheData';
import { AUTH_INTERFACE } from '../app-provider/authInterface';
import { useSetRamAuthInterfaceStatus, useGetRamAuthStatus } from '../app-provider';
import BaseAuthMessage from '../shared/AuthMessage/BaseAuthMessage';
import { isIntlSite } from '~/utils'; 
import { createService } from '@ali/cnd';

export default ({ history, gatewayType }) => {
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const regionList = CachedData.regionList;
  const authDispatch = useSetRamAuthInterfaceStatus();
  const resourceAuthStatus = useGetRamAuthStatus(AUTH_INTERFACE.GET_RESOURCE_OVERVIEW);

  useEffect(() => {
    fetchData();
    createService('kms')([
      {
        action: 'ListKeys',
        version: '2016-01-20',
        product: 'kms',
        customRequestKey: '"kst-hzz653618faw5zzahfnmr',
        params: {
          KeyId: '"kst-hzz653618faw5zzahfnmr',
          PageNumber: 1,
          PageSize: 10
        }
      },
      {
        "action": "ListKeys",
        "version": "2016-01-20",
        "product": "kms",
        "customRequestKey": "kst-hzz6613a8f7vzpxjcxgjz",
        "data": {
          KeyId: 'kst-hzz6613a8f7vzpxjcxgjz',
          PageNumber: 1,
          PageSize: 10
        }
      }
    ])
  }, []);

  const fetchData = () => {
    setLoading(true);
    const promises = map(regionList, (region) => {
      return GetResourceList(region);
    });
    Promise.all(promises)
      .then((res) => {
        setDataSource(res.filter(Boolean));
        setLoading(false);
      })
      .catch(() => {
        setDataSource([]);
        setLoading(false);
      });
  };

  const formattedResourceData = (data) => {
    const resourceTypes = [];
    map(data, (value, item) => {
      if (!isNil(get(value, 'totalCount'))) {
        resourceTypes.push({
          type: item,
          totalCount: get(value, 'totalCount'),
        });
      } else {
        const subResourceTypes = formattedResourceData(value);
        resourceTypes.push(...subResourceTypes);
      }
    });
    return resourceTypes;
  };

  const GetResourceList = async (region) => {
    let data = await services.GetResourceOverview(region.id)({
      params: {
        gatewayType,
      },
      customErrorHandle: (err, data, callback) => {
        if (err?.code === 'Forbidden.AccessDenied') {
          authDispatch(AUTH_INTERFACE.GET_RESOURCE_OVERVIEW, false);
        } else {
          callback();
        }
      },
    });
    if (data) {
      authDispatch(AUTH_INTERFACE.GET_RESOURCE_OVERVIEW, true);
    }
    const resourceTypes = formattedResourceData(data);
    return some(resourceTypes, (resource) => resource.totalCount > 0)
      ? {
          ...data,
          region: region.id,
          name: region.name,
        }
      : null;
  };

  const handletoLink = (type, region) => {
    sessionStorage.setItem('apiListType', type);
    window.regionId = region;
    if (isGrayRegion()) {
      window.location.href = `${window.location.origin}/?region=${window.regionId}#/${window.regionId}/api-manage/api-list`;
    } else {
      history.push(`/${window.regionId}/api-manage/api-list`);
    }
  };

  const getGatewayList = async () => {
    let {
      items = [],
      totalSize,
      pageSize,
      pageNumber,
    } = await services.getGatewayList({
      params: {
        pageNumber: 1,
        pageSize: 5,
        gatewayType: 'AI',
      },
    });
    return first(items) || {};
  };

  const isGrayRegion = () => {
    const {
      type = 'user',
      enabled = false,
      grayRegions = [],
    } = (window.ALIYUN_CONSOLE_CONFIG as any).VERSION_GRAY_CONFIG || {};
    console.log((window.ALIYUN_CONSOLE_CONFIG as any).VERSION_GRAY_CONFIG, 'VERSION_GRAY_CONFIG');
    return type === 'region' && enabled && !isEmpty(grayRegions);
  };

  return (
    <div className="overview-basic-layout">
      <h3 className="f-w-500 mb-16">
        {intl('apigw.components.overview.ResourceList.ResourceStatistics')}
      </h3>
      <div className="mb-8 space-between">
        <div>
          <Button
            type="primary"
            onClick={() => {
              const createUrl = CachedData.confLink(
                gatewayType === 'API'
                  ? 'feature:gateway:common:buy'
                  : 'feature:ai:gateway:common:buy',
                {
                  regionId: CachedData.getCurrentRegionId(),
                },
              );
              window.open(createUrl, '_blank');
            }}
          >
            {intl('apigw.components.overview.ActionsGrid.CreateAnInstance')}
          </Button>
          {!isIntlSite() && (
            <Button className="ml-8" onClick={() => window.open(CachedData.confLink('free:trial'))}>
              {intl('apigw.components.overview.QuickApiFlow.FreeTrial')}
            </Button>
          )}
          <Button
            className="ml-8"
            onClick={() => {
              window.open(CachedData.confLink('api:type:doc') as any);
            }}
          >
            {intl('apigw.components.overview.QuickApiFlow.ApiTypeDescription')}
          </Button>
        </div>
        <Button onClick={() => fetchData()}>
          <Icon type="arrow_circular" />
        </Button>
      </div>
      <Table
        dataSource={dataSource}
        loading={loading}
        columns={
          [
            {
              title: intl('apigw.components.overview.ResourceList.Region'),
              dataIndex: 'region',
              cell: (value, index, record) => {
                return <div>{intl(get(record, 'name', ''))}</div>;
              },
            },
            {
              title: (
                <span>
                  {intl('apigw.components.overview.ResourceList.NumberOfInstancesRunningNormally')}
                </span>
              ),

              dataIndex: 'gateway',
              width: '20%',
              cell: (value, index, record) => {
                const { totalCount, runningCount } = value || {};
                return isEmpty(value) ? (
                  <Icon type="loading" size="small" />
                ) : (
                  <div>
                    <span className="f-w-500" style={{ color: '#009431' }}>
                      {runningCount}
                    </span>
                    /{totalCount}
                  </div>
                );
              },
            },
            gatewayType === 'API' && {
              title: (
                <span>
                  {intl('apigw.components.overview.ResourceListSync.RestApiPublishedTotal')}
                  {/* <Balloon
              closable={false}
              trigger={<Icon size="xs" type="help" className="ml-4" />}
              >
              当API存在多版本时，该API数量将按照版本分别计数
              </Balloon> */}
                </span>
              ),

              key: 'rest',
              dataIndex: 'httpApi.rest',
              width: '20%',
              cell: (value, index, record) => {
                const { region } = record;
                const { totalCount, deployedCount } = value || {};
                return isEmpty(value) ? (
                  <Icon type="loading" size="small" />
                ) : totalCount === 0 ? (
                  0
                ) : (
                  <Button
                    type="primary"
                    text
                    className="f-w-500"
                    onClick={() => {
                      handletoLink('Rest', region);
                    }}
                  >
                    {`${deployedCount}/${totalCount}`}
                  </Button>
                );
              },
            },
            gatewayType === 'API' && {
              title: 'HTTP API',
              key: 'http',
              dataIndex: 'httpApi.http',
              cell: (value, index, record) => {
                const { region } = record;
                return isEmpty(value) ? (
                  <Icon type="loading" size="small" />
                ) : value?.totalCount === 0 ? (
                  0
                ) : (
                  <Button
                    type="primary"
                    text
                    className="f-w-500"
                    onClick={() => {
                      handletoLink('Http', region);
                    }}
                  >
                    {value?.totalCount}
                  </Button>
                );
              },
            },
            gatewayType === 'API' && {
              title: 'WebSocket API',
              key: 'websocket',
              dataIndex: 'httpApi.websocket',
              cell: (value, index, record) => {
                const { region } = record;
                return isEmpty(value) ? (
                  <Icon type="loading" size="small" />
                ) : value?.totalCount === 0 ? (
                  0
                ) : (
                  <Button
                    type="primary"
                    text
                    className="f-w-500"
                    onClick={() => {
                      handletoLink('Websocket', region);
                    }}
                  >
                    {value?.totalCount}
                  </Button>
                );
              },
            },
            gatewayType === 'API' && {
              title: 'HTTP (Ingress)',
              key: 'httpIngress',
              dataIndex: 'httpApi.httpIngress',
              cell: (value, index, record) => {
                const { region } = record;
                return isEmpty(value) ? (
                  <Icon type="loading" size="small" />
                ) : value?.totalCount === 0 ? (
                  0
                ) : (
                  <Button
                    type="primary"
                    text
                    className="f-w-500"
                    onClick={() => {
                      handletoLink('HttpIngress', region);
                    }}
                  >
                    {value?.totalCount}
                  </Button>
                );
              },
            },
            gatewayType === 'AI' &&
              false && {
                title: intl('apigw.components.overview.ResourceListSync.LlmApiPublishedTotal'),
                key: 'LLM',
                dataIndex: 'httpApi.llm',
                cell: (value, index, record) => {
                  const { region } = record;
                  return isEmpty(value) ? (
                    <Icon type="loading" size="small" />
                  ) : value?.totalCount === 0 ? (
                    0
                  ) : (
                    <Button
                      type="primary"
                      text
                      className="f-w-500"
                      onClick={() => {
                        handletoLink('LLM', region);
                      }}
                    >
                      {value?.totalCount}
                    </Button>
                  );
                },
              },
            gatewayType === 'AI' &&
              false && {
                title: intl('apigw.components.overview.ResourceListSync.McpsPublishedTotal'),
                key: 'MCP',
                dataIndex: 'httpApi.mcp',
                cell: (value, index, record) => {
                  const { region } = record;
                  return isEmpty(value) ? (
                    <Icon type="loading" size="small" />
                  ) : value?.totalCount === 0 ? (
                    0
                  ) : (
                    <Button
                      type="primary"
                      text
                      className="f-w-500"
                      onClick={() => {
                        handletoLink('MCP', region);
                      }}
                    >
                      {value?.totalCount}
                    </Button>
                  );
                },
              },
            {
              title: intl('apigw.components.overview.ResourceList.Operation'),
              cell: (value, index, record) => {
                const { gateway = {}, region } = record;
                const { totalCount } = gateway;
                return (
                  // @ts-ignore
                  <Actions>
                    {totalCount === 0 ? (
                      <ExternalLink
                        label={intl('apigw.components.overview.ActionsGrid.CreateAnInstance')}
                        url={CachedData.confLink('feature:gateway:common:buy', {
                          regionId: region,
                        })}
                      />
                    ) : (
                      <ExternalLink
                        label={intl(
                          'apigw.components.overview.ResourceListSync.InstanceManagement',
                        )}
                        url={`/#/${region}/${gatewayType === 'API' ? 'gateway' : 'ai-gateway'}`}
                        self
                        icon={false}
                      />
                    )}
                    {/* <ExternalLink
                   label={intl('apigw.components.overview.ResourceList.ApiManagement')}
                   url={`/#/${region}/api-manage/api-list`}
                   handleClick={() => {
                     window.regionId = region;
                     document.cookie = `currentRegionId=${region};path=/`;
                   }}
                   self
                   icon={false}
                  /> */}
                  </Actions>
                );
              },
            },
          ].filter(Boolean) as any
        }
        emptyContent={
          !resourceAuthStatus ? (
            <BaseAuthMessage authType={AUTH_INTERFACE.GET_RESOURCE_OVERVIEW} type="text" />
          ) : (
            intl('apigw.components.overview.ResourceListSync.NoDataYet')
          )
        }
      />
    </div>
  );
};
